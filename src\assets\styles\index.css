/* 自定义全局样式 */
:root {
  --clash-primary: #1976d2;
  --clash-primary-light: rgba(25, 118, 210, 0.12);
  --clash-success: #4caf50;
  --clash-warning: #ff9800;
  --clash-error: #f44336;
  --clash-background: #f5f5f5;
  --clash-card-bg: #ffffff;
  --clash-item-bg: #ffffff;
  --clash-tag-bg: #f0f0f0;
  --clash-text-primary: #333333;
  --clash-text-secondary: #666666;
  --clash-border-color: #e0e0e0;
}



.clash-container {
  max-width: 100%;
  padding: 0;
  background-color: var(--clash-background);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 60px;
}

.clash-card {
  background-color: var(--clash-card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  overflow: hidden;
}

/* 代理节点项 */
.proxy-item {
  background-color: var(--clash-item-bg);
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  transition: all 0.2s ease;
}

.proxy-item:last-child {
  margin-bottom: 0;
}

.proxy-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.proxy-item.selected {
  background-color: var(--clash-primary-light);
}

.proxy-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--clash-primary);
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

/* 代理节点顶部行 */
.proxy-top-row {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--clash-text-primary);
  padding-left: 2px;
}

/* 代理节点底部行 */
.proxy-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  width: 100%;
  padding-top: 6px;
}

/* 代理类型 */
.proxy-type {
  color: var(--clash-text-secondary);
  padding: 0;
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  text-align: left;
  opacity: 0.8;
}

/* 延迟 */
.proxy-latency {
  font-weight: 600;
  font-size: 12px;
  white-space: nowrap;
  text-align: right;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 延迟状态颜色 */
.latency-fast { color: #67c23a; font-weight: 600; }
.latency-medium { color: #e6a23c; font-weight: 600; }
.latency-slow { color: #f56c6c; font-weight: 600; }
.latency-timeout { color: #909399; font-weight: normal; opacity: 0.7; }
.latency-failed { color: #8e44ad; font-weight: 600; }

/* 空状态 */
.empty-state {
  padding: 30px 16px;
  text-align: center;
  color: var(--clash-text-secondary);
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

/* 错误消息 */
.error-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 16px;
  border-radius: 4px;
  z-index: 1001;
}

/* 滚动样式 */
.scrollable {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;

  /* 隐藏默认滚动条 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent transparent; /* Firefox */
}

/* 自定义WebKit滚动条 */
.scrollable::-webkit-scrollbar {
  width: 5px;
}

.scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
  transition: background-color 0.3s;
}

/* 滚动时显示滚动条 */
.scrollable:hover::-webkit-scrollbar-thumb,
.scrollable:active::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
}

/* Firefox滚动时显示滚动条 */
.scrollable:hover,
.scrollable:active {
  scrollbar-color: rgba(144, 147, 153, 0.3) transparent; /* Firefox */
}

.section-title {
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--clash-text-primary);
}

.tab-container {
  background-color: var(--clash-card-bg);
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 定制vant组件样式 */
.van-nav-bar, .top-nav {
  background-color: var(--clash-primary);
  height: 50px;
  line-height: 50px;
  flex-grow: 0;
  flex-shrink: 0;
}

.van-nav-bar__content {
  height: 50px;
}

.van-nav-bar__title {
  color: white;
  height: 50px;
  line-height: 50px;
}

.van-nav-bar .van-icon {
  color: white !important;
}

.van-nav-bar__placeholder {
  height: 50px !important;
  flex-shrink: 0;
  flex-grow: 0;
}

.van-tabs__line {
  background-color: var(--clash-primary);
}

.van-dropdown-menu__bar {
  background-color: transparent;
}

.drop-item {
  padding: 10px 0;
}

.drop-item-text {
  font-size: 14px;
}

.van-hairline--top-bottom::after,
.van-hairline-unset--top-bottom::after {
  border-width: 0;
}
