import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import { Button, Tabbar, TabbarItem, Tab, Tabs, Cell, CellGroup, List, NavBar, 
  DropdownItem, DropdownMenu, Loading, PullRefresh, Icon, Field, Switch, Toast, Form, Popup, Picker, Dialog, Empty } from 'vant';

// 引入vant样式
import 'vant/lib/index.css';
// 引入自定义样式
import './assets/styles/index.css';

// 初始化应用数据
store.dispatch('initData');

const app = createApp(App);

// 注册Vant组件
const components = [
  Button, Tabbar, TabbarItem, Tab, Tabs, Cell, CellGroup, List, NavBar,
  DropdownItem, DropdownMenu, Loading, PullRefresh, Icon, Field, Switch, Toast, Form, Popup, Picker, Dialog, Empty
];

components.forEach(component => {
  app.use(component);
});

app.use(router)
   .use(store)
   .mount('#app'); 