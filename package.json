{"name": "clash-mobile-ui", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.2", "js-yaml": "^4.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vant": "^4.9.17", "vite": "^6.2.1", "vite-plugin-pwa": "^0.21.1"}}