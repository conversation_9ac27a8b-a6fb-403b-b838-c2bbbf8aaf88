<template>
  <div class="subscription-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="订阅详情"
      left-text="返回"
      left-arrow
      :fixed="true"
      :placeholder="true"
      @click-left="$router.back()"
      class="detail-nav-bar"
    >
      <template #right>
        <van-button 
          type="primary" 
          size="small"
          @click="saveSubscription"
          :loading="saving"
          class="save-btn"
        >
          保存
        </van-button>
      </template>
    </van-nav-bar>

    <div class="subscription-content">
      <van-loading v-if="loading" type="spinner" color="#1989fa" vertical>加载中...</van-loading>
      <van-field
        v-else
        v-model="subscriptionContent"
        type="textarea"
        rows="20"
        placeholder="订阅内容将在这里显示..."
        class="subscription-textarea full-height-textarea"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { showSuccessToast, showFailToast, showConfirmDialog } from 'vant';
import axios from 'axios';
import yaml from 'js-yaml';

export default {
  name: 'SubscriptionDetail',
  setup() {
    const store = useStore();
    const route = useRoute();
    
    const subscriptionUrl = ref('');
    const subscriptionContent = ref('');
    const originalContent = ref('');
    const saving = ref(false);
    const loading = ref(true);
    
    // 获取请求头
    const getHeaders = (contentType = null) => {
      const headers = {};
      if (store.state.apiSecret) {
        headers['Authorization'] = `Bearer ${store.state.apiSecret}`;
      }
      if (contentType) {
        headers['Content-Type'] = contentType;
      }
      return headers;
    };
    
    onMounted(async () => {
      // 从路由获取订阅URL
      subscriptionUrl.value = route.params.url || '';
      if (subscriptionUrl.value) {
        await fetchContent();
      }
    });
    
    // 获取订阅内容
    const fetchContent = async () => {
      loading.value = true;
      try {
        const response = await axios.get(subscriptionUrl.value, {
          headers: getHeaders()
        });
        subscriptionContent.value = response.data;
        originalContent.value = response.data;
      } catch (error) {
        console.error('获取订阅内容失败:', error);
        showFailToast('获取订阅内容失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 验证YAML格式
    const validateYaml = (content) => {
      try {
        yaml.load(content);
        return { valid: true };
      } catch (error) {
        return { 
          valid: false, 
          error: error.message
        };
      }
    };
    
    // 保存订阅
    const saveSubscription = async () => {
      // 验证YAML格式
      const validation = validateYaml(subscriptionContent.value);
      
      if (!validation.valid) {
        // 显示确认对话框
        try {
          await showConfirmDialog({
            title: 'YAML格式错误',
            message: `检测到YAML格式错误：\n${validation.error}\n\n是否仍要保存？`,
            confirmButtonText: '继续保存',
            cancelButtonText: '返回修改'
          });
          // 用户选择继续保存
        } catch (e) {
          // 用户选择返回修改
          return;
        }
      }
      
      saving.value = true;
      try {
        // 先发送更新请求
        await axios.post(subscriptionUrl.value, subscriptionContent.value, {
          headers: getHeaders('text/plain')
        });
        
        // 重新获取最新内容
        const response = await axios.get(subscriptionUrl.value, {
          headers: getHeaders()
        });
        
        // 更新显示内容
        subscriptionContent.value = response.data;
        originalContent.value = response.data;
        
        showSuccessToast(validation.valid ? '订阅已更新' : '订阅已更新，但YAML格式有误');
      } catch (error) {
        console.error('更新订阅失败:', error);
        showFailToast('更新订阅失败');
      } finally {
        saving.value = false;
      }
    };
    
    return {
      subscriptionUrl,
      subscriptionContent,
      saving,
      loading,
      saveSubscription
    };
  }
};
</script>

<style scoped>

.subscription-detail {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  width: 100%;
  overflow: hidden;
  border-bottom: 1px solid #e0e0e0;
}

.subscription-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: relative;
}

.save-btn {
  height: 28px;
  font-size: 12px;
  padding: 0 12px;
}

.subscription-textarea {
  flex: 1;
  display: flex;
  padding: 0;
  margin: 0;
}

.full-height-textarea {
  flex: 1;
}

:deep(.van-cell) {
  flex: 1;
  display: flex;
  padding: 0;
  border-radius: 0;
  background-color: white;
}

:deep(.van-field__control) {
  flex: 1;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 12px;
  resize: none;
  background-color: white;
}

:deep(.van-field__body) {
  display: flex;
  flex: 1;
  height: 100%;
  
}

:deep(.van-field__body textarea) {
  flex: 1;
  height: 100%;
}

/* 导航栏样式 */
:deep(.detail-nav-bar .van-nav-bar__left) {
  color: white;
}

:deep(.detail-nav-bar .van-nav-bar__text) {
  color: white;
}

:deep(.detail-nav-bar .van-icon) {
  color: white;
}

:deep(.detail-nav-bar .van-button) {
  vertical-align: middle;
}

.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
</style> 