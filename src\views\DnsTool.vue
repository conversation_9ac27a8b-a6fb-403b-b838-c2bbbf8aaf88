<template>
  <div class="dns-tool clash-container">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="DNS查询工具"
      left-text="返回"
      left-arrow
      :fixed="true"
      :placeholder="true"
      @click-left="$router.back()"
      class="dns-nav-bar"
    />

    <div class="dns-content">
      <!-- 百度风格的DNS查询界面 -->
      <div class="baidu-style-search">
        <!-- DNS Logo -->
        <div class="dns-logo">
          <div class="logo-text">DNS</div>
          <div class="logo-subtitle">域名解析查询</div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-container">
          <div class="search-box">
            <!-- 域名输入框 -->
            <input 
              type="text"
              v-model="domain"
              class="domain-input"
              placeholder="查询的域名"
            />
            
            <!-- 记录类型选择器 -->
            <div class="record-type-selector" @click="toggleTypeList">
              <span class="type-value">{{ queryType }}</span>
              <span class="type-arrow" :class="{ expanded: showTypeList }">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                  <path d="M7 10l5 5 5-5z"/>
                </svg>
              </span>
              
              <!-- 下拉菜单 -->
              <div class="type-dropdown" v-show="showTypeList">
                <div 
                  v-for="type in queryTypes" 
                  :key="type" 
                  class="type-item" 
                  :class="{ active: queryType === type }"
                  @click.stop="selectType(type)"
                >
                  {{ type }}
                </div>
              </div>
            </div>

            <!-- 查询按钮 -->
            <button 
              class="search-button" 
              @click="performDnsQuery"
              :disabled="querying"
            >
              <span v-if="!querying">查询</span>
              <van-loading v-else type="spinner" size="20px" color="white" />
            </button>
          </div>
        </div>
      </div>

      <!-- 查询结果 -->
      <div class="result-card" v-if="queryResult">
        <div class="result-header">查询结果</div>
        
        <!-- 显示问题信息 -->
        <div class="result-basic-info">
          <div class="result-row">
            <span class="result-label">查询域名:</span>
            <span class="result-value">{{ queryResult.Question?.[0]?.Name }}</span>
          </div>
          <div class="result-row">
            <span class="result-label">查询状态:</span>
            <span class="result-value">{{ formatStatus(queryResult.Status) }}</span>
          </div>
        </div>
        
        <!-- 答案部分 -->
        <template v-if="queryResult.Answer && queryResult.Answer.length > 0">
          <div class="result-section">
            <div class="result-title">解析结果</div>
            <div class="result-item" v-for="(answer, index) in queryResult.Answer" :key="index">
              <div class="result-row">
                <span class="result-label">IP地址:</span>
                <span class="result-value">{{ answer.data }}</span>
              </div>
              <div class="result-row">
                <span class="result-label">名称:</span>
                <span class="result-value">{{ answer.name }}</span>
              </div>
              <div class="result-row">
                <span class="result-label">TTL:</span>
                <span class="result-value">{{ answer.TTL }}秒</span>
              </div>
              <div class="result-row">
                <span class="result-label">类型:</span>
                <span class="result-value">{{ formatRecordType(answer.type) }}</span>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="no-result">未找到DNS记录</div>
        </template>
        
        <!-- DNS标志 -->
        <div class="result-section">
          <div class="result-title">DNS标志</div>
          <div class="flags-grid">
            <div class="flag-item">
              <span class="flag-label">递归查询(RD):</span>
              <span class="flag-value">{{ queryResult.RD ? '是' : '否' }}</span>
            </div>
            <div class="flag-item">
              <span class="flag-label">递归可用(RA):</span>
              <span class="flag-value">{{ queryResult.RA ? '是' : '否' }}</span>
            </div>
            <div class="flag-item">
              <span class="flag-label">截断(TC):</span>
              <span class="flag-value">{{ queryResult.TC ? '是' : '否' }}</span>
            </div>
            <div class="flag-item">
              <span class="flag-label">验证已禁用(CD):</span>
              <span class="flag-value">{{ queryResult.CD ? '是' : '否' }}</span>
            </div>
            <div class="flag-item">
              <span class="flag-label">权威答案(AA):</span>
              <span class="flag-value">{{ queryResult.AA ? '是' : '否' }}</span>
            </div>
            <div class="flag-item">
              <span class="flag-label">DNS广告服务(AD):</span>
              <span class="flag-value">{{ queryResult.AD ? '是' : '否' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { showSuccessToast, showFailToast } from 'vant';
import axios from 'axios';

export default {
  name: 'DnsTool',
  setup() {
    const store = useStore();
    
    // 查询状态和表单数据
    const domain = ref('');
    const queryType = ref('A');
    const queryTypes = ['A', 'AAAA', 'CNAME', 'MX', 'NS', 'TXT', 'SOA', 'SRV'];
    const querying = ref(false);
    const queryResult = ref(null);
    const showTypeList = ref(false); // 控制类型列表的显示/隐藏
    
    // 切换记录类型列表显示状态
    const toggleTypeList = () => {
      showTypeList.value = !showTypeList.value;
      
      // 重新添加定位代码，确保下拉菜单显示在正确位置
      if (showTypeList.value) {
        // 延迟执行确保DOM已更新
        setTimeout(() => {
          const selector = document.querySelector('.record-type-selector');
          const dropdown = document.querySelector('.type-dropdown');
          
          if (selector && dropdown) {
            const rect = selector.getBoundingClientRect();
            dropdown.style.top = `${rect.bottom}px`;
            dropdown.style.left = `${rect.left}px`;
            dropdown.style.width = `${rect.width}px`;
          }
        }, 10);
      }
    };
    
    // 选择记录类型
    const selectType = (type) => {
      queryType.value = type;
      showTypeList.value = false; // 选择后关闭列表
    };
    
    // 点击页面其他区域关闭列表
    const closeTypeList = (event) => {
      const selector = document.querySelector('.record-type-selector');
      if (selector && !selector.contains(event.target)) {
        showTypeList.value = false;
      }
    };
    
    // 添加全局点击事件监听器
    onMounted(() => {
      document.addEventListener('click', closeTypeList);
    });
    
    // 移除事件监听器
    onBeforeUnmount(() => {
      document.removeEventListener('click', closeTypeList);
    });
    
    // 获取请求头
    const getHeaders = () => {
      const headers = {};
      if (store.state.apiSecret) {
        headers['Authorization'] = `Bearer ${store.state.apiSecret}`;
      }
      return headers;
    };
    
    // 执行DNS查询
    const performDnsQuery = async () => {
      if (!domain.value) {
        showFailToast('请输入域名');
        return;
      }
      
      querying.value = true;
      try {
        const response = await axios.get(`${store.state.apiBaseUrl}/dns/query`, {
          params: {
            name: domain.value,
            type: queryType.value
          },
          headers: getHeaders()
        });
        
        queryResult.value = response.data;
        showSuccessToast('查询成功');
      } catch (error) {
        console.error('DNS查询失败:', error);
        showFailToast('DNS查询失败');
      } finally {
        querying.value = false;
      }
    };
    
    // 格式化状态码
    const formatStatus = (status) => {
      const statusCodes = {
        0: '无错误',
        1: '格式错误',
        2: '服务器故障',
        3: '不存在域名',
        4: '不支持的请求类型',
        5: '查询被拒绝'
      };
      
      return statusCodes[status] || `未知状态(${status})`;
    };
    
    // 格式化记录类型
    const formatRecordType = (type) => {
      const recordTypes = {
        1: 'A',
        2: 'NS',
        5: 'CNAME',
        6: 'SOA',
        12: 'PTR',
        15: 'MX',
        16: 'TXT',
        28: 'AAAA',
        33: 'SRV',
        65: 'HTTPS'
      };
      
      return recordTypes[type] || `类型${type}`;
    };
    
    return {
      domain,
      queryType,
      queryTypes,
      querying,
      queryResult,
      showTypeList,
      toggleTypeList,
      performDnsQuery,
      formatStatus,
      formatRecordType,
      selectType
    };
  }
};
</script>

<style scoped>
.dns-tool {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.dns-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
}

/* 导航栏样式 */
:deep(.dns-nav-bar .van-nav-bar__left),
:deep(.dns-nav-bar .van-nav-bar__text),
:deep(.dns-nav-bar .van-icon) {
  color: white !important;
}

/* 百度风格的搜索界面 */
.baidu-style-search {
  width: 100%;
  max-width: 650px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40px;
}

/* DNS Logo */
.dns-logo {
  text-align: center;
  margin-bottom: 30px;
}

.logo-text {
  font-size: 56px;
  font-weight: bold;
  color: var(--clash-primary);
  line-height: 1.2;
  letter-spacing: -1px;
}

.logo-subtitle {
  font-size: 16px;
  color: var(--clash-text-secondary);
  margin-top: 5px;
}

/* 搜索容器 */
.search-container {
  width: 100%;
  position: relative;
}

.search-box {
  display: flex;
  justify-content: space-between;
  height: 44px;
  width: 100%;
  border: 2px solid var(--clash-primary);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 域名输入框 */
.domain-input {
  flex: 1;
  min-width: 0; /* 确保域名输入框可以收缩 */
  border: none;
  outline: none;
  padding: 0 15px;
  font-size: 16px;
}

/* 记录类型选择器 */
.record-type-selector {
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: #f8f8f8;
  border-left: 1px solid #e8e8e8;
  cursor: pointer;
  user-select: none;
}

.type-value {
  font-size: 14px;
  color: var(--clash-text-primary);
}

.type-arrow {
  margin-left: 4px;
  display: flex;
  align-items: center;
  transition: transform 0.3s;
}

.type-arrow.expanded {
  transform: rotate(180deg);
}

/* 下拉菜单 */
.type-dropdown {
  position: fixed;
  background-color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  z-index: 99;
  overflow: hidden;
  min-width: 100px;
  margin-top: 3px;
}

.type-item {
  padding: 10px 16px;
  font-size: 14px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  white-space: nowrap;
  background-color: white;
}

.type-item:last-child {
  border-bottom: none;
}

.type-item.active {
  background-color: var(--clash-primary);
  color: white;
}

.type-item:hover {
  background-color: #f5f5f5;
}

.type-item.active:hover {
  background-color: var(--clash-primary);
}

/* 查询按钮 */
.search-button {
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: white;
  background-color: var(--clash-primary);
  border: none;
  cursor: pointer;
}

.search-button:hover {
  background-color: var(--clash-primary-dark, #1e88e5);
}

.search-button:disabled {
  opacity: 0.8;
  cursor: not-allowed;
}

/* 结果卡片 */
.result-card {
  width: 100%;
  max-width: 650px;
  margin-top: 30px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.result-header {
  padding: 14px 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--clash-text-primary);
  background-color: #f8f8f8;
  border-bottom: 1px solid #e8e8e8;
}

.result-basic-info {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.no-result {
  padding: 30px 0;
  text-align: center;
  color: var(--clash-text-secondary);
  font-size: 15px;
}

/* 结果部分 */
.result-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.result-section:last-child {
  border-bottom: none;
}

.result-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--clash-text-primary);
  margin-bottom: 12px;
}

.result-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 14px;
  margin-bottom: 12px;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-row {
  display: flex;
  margin-bottom: 8px;
}

.result-row:last-child {
  margin-bottom: 0;
}

.result-label {
  min-width: 70px;
  font-weight: 500;
  color: var(--clash-text-secondary);
  font-size: 14px;
}

.result-value {
  flex: 1;
  word-break: break-all;
  color: var(--clash-text-primary);
  font-size: 14px;
}

/* DNS标志网格布局 */
.flags-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.flag-item {
  display: flex;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.flag-label {
  color: var(--clash-text-secondary);
  font-size: 14px;
  margin-right: 5px;
}

.flag-value {
  color: var(--clash-text-primary);
  font-size: 14px;
  font-weight: 500;
}


</style> 