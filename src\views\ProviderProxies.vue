<template>
  <div class="provider-proxies">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      :title="providerName"
      left-text="返回"
      left-arrow
      :fixed="true"
      :placeholder="true"
      @click-left="goBack"
    />

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 下拉刷新组件 -->
      <van-pull-refresh 
        v-model="refreshing" 
        @refresh="onRefresh" 
        :disabled="loading || !isAtTop"
        :head-height="50"
        success-text="刷新成功"
        pulling-text="下拉刷新"
        loosing-text="释放刷新"
      >
        <div class="proxy-container" ref="containerRef" @scroll="onScroll">
          <!-- 节点列表 -->
          <div class="proxy-grid">
            <div 
              v-for="(proxy, index) in proxies" 
              :key="proxy.name + index" 
              class="proxy-item"
              @click="testSingleProxy(proxy.name)">
              <div class="proxy-top-row">{{ proxy.name }}</div>
              <div class="proxy-bottom-row">
                <span class="proxy-type">{{ proxy.type }}</span>
                <span 
                  class="proxy-latency" 
                  :class="latencyClass(proxy)">
                  {{ formatLatency(proxy) }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 空状态显示 -->
          <div v-if="proxies.length === 0 && !loading" class="empty-state">
            <p>此代理集合中没有节点</p>
          </div>
          
          <!-- 加载中状态 -->
          <div v-if="loading" class="loading-state">
            <van-loading type="spinner" color="var(--clash-primary)" />
            <p>加载中...</p>
          </div>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { showSuccessToast, showFailToast, showLoadingToast, closeToast } from 'vant';
import axios from 'axios';

export default {
  name: 'ProviderProxies',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const store = useStore();
    
    // 获取URL参数中的provider名称
    const providerName = ref(route.params.name || '未知集合');
    
    // 状态
    const proxies = ref([]);
    const loading = ref(true);
    const refreshing = ref(false);
    const isAtTop = ref(true);
    const containerRef = ref(null);
    
    // 获取请求头
    const getHeaders = () => {
      const headers = {};
      if (store.state.apiSecret) {
        headers['Authorization'] = `Bearer ${store.state.apiSecret}`;
      }
      return headers;
    };
    
    // 监听滚动事件，判断是否在顶部
    const onScroll = () => {
      if (containerRef.value) {
        // 如果滚动位置小于或等于10px，认为在顶部
        isAtTop.value = containerRef.value.scrollTop <= 10;
      }
    };
    
    // 返回上一页
    const goBack = () => {
      router.push('/provider');
    };
    
    // 获取代理集合详情
    const fetchProviderProxies = async () => {
      loading.value = true;
      try {
        const response = await axios.get(`${store.state.apiBaseUrl}/providers/proxies/${encodeURIComponent(providerName.value)}`, { 
          headers: getHeaders() 
        });
        
        // 直接使用响应数据中的 proxies 数组
        if (response.data && Array.isArray(response.data.proxies)) {
          proxies.value = response.data.proxies.map(proxy => {
            let history = proxy.history;
          
            let uncheck = true;
            let broken = false;
            let bad = false;
            let fine = false;

            //计算平均时延
            if(history && history.length > 0){
              let fineHistory = history.filter(record => record.delay > 0);
              let failHisotry = history.filter(record => record.delay === 0);

              broken = failHisotry.length > 0 && fineHistory.length > 0; // 是否断流
              bad = fineHistory.length === 0; // 是否超时
              fine = failHisotry.length === 0; // 是否正常

              uncheck = false;

              proxy.averageDelay = Math.round(fineHistory.reduce((sum, record) => sum + record.delay, 0) / fineHistory.length);
              proxy.latestDelay = history[history.length - 1].delay;
              
              // 计算在线率
              proxy.onlineRate = Math.floor(((history.length - failHisotry.length) / history.length) * 100);
            }

            return {
              ...proxy,
              uncheck,
              bad,
              broken,
              fine
            };
          });
        } else {
          proxies.value = [];
          showFailToast('找不到代理节点数据');
        }
      } catch (error) {
        console.error('获取代理集合详情失败:', error);
        showFailToast('获取代理集合详情失败');
        proxies.value = [];
      } finally {
        loading.value = false;
      }
    };
    
    // 测试单个代理
    const testSingleProxy = async (proxyName) => {
      try {
        const toast = showLoadingToast({
          message: '正在测试节点...',
          forbidClick: true,
          duration: 0,
        });
        
        const timeout = localStorage.getItem('timeout') || '5000';
        const testUrl = localStorage.getItem('testUrl') || 'http://www.gstatic.com/generate_204';
        
        await axios.get(`${store.state.apiBaseUrl}/providers/proxies/${encodeURIComponent(providerName.value)}/${encodeURIComponent(proxyName)}/healthcheck`, {
          params: {
            timeout: parseInt(timeout),
            url: testUrl
          },
          headers: getHeaders()
        });
        
        closeToast();
        showSuccessToast('测试完成');
        
        // 刷新数据
        await fetchProviderProxies();
      } catch (error) {
        closeToast();
        showFailToast('测试失败');
        console.error('测试节点失败:', error);
      }
    };
    
    // 下拉刷新
    const onRefresh = async () => {
      try {
        await fetchProviderProxies();
      } catch (error) {
        console.error('刷新失败:', error);
      } finally {
        refreshing.value = false;
      }
    };
    
    // 格式化延迟信息
    const formatLatency = (proxy) => {
      if (proxy.uncheck) {
        return '待测试';
      }
      
      if (proxy.bad) {
        return '离线';
      } else if (proxy.broken) {
        return '断流 ' + proxy.onlineRate + '%';
      }
      
      return `${proxy.averageDelay} ms`;
    };
    
    // 获取延迟对应的样式类
    const latencyClass = (proxy) => {
      if (proxy.uncheck || proxy.bad) {
        return 'latency-timeout';
      } else if (proxy.averageDelay < 200) {
        return 'latency-fast';
      } else if (proxy.averageDelay < 500) {
        return 'latency-medium';
      } else if (proxy.averageDelay < 1000) {
        return 'latency-slow';
      } else {
        return 'latency-failed';
      }
    };

    // 初始化
    onMounted(() => {
      fetchProviderProxies();
    });
    
    return {
      providerName,
      proxies,
      loading,
      refreshing,
      isAtTop,
      containerRef,
      goBack,
      onRefresh,
      onScroll,
      formatLatency,
      latencyClass,
      testSingleProxy
    };
  }
};
</script>

<style scoped>
.provider-proxies {
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: var(--clash-background);
  overflow: hidden;
  flex: 1;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 代理节点容器 */
.proxy-container {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 8px 12px;
  box-sizing: border-box;
  overscroll-behavior: contain;
}

/* 下拉刷新组件样式 */
:deep(.van-pull-refresh) {
  overflow: visible;
  height: 100%;
}

/* 代理节点网格 */
.proxy-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 8px 0 50px;
}

/* 代理节点项 */
.proxy-item {
  background-color: var(--clash-item-bg);
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  transition: all 0.2s ease;
}

.proxy-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 代理节点顶部和底部行 */
.proxy-top-row {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--clash-text-primary);
  padding-left: 2px;
}

.proxy-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  width: 100%;
  padding-top: 6px;
}

/* 代理类型和延迟 */
.proxy-type {
  color: var(--clash-text-secondary);
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  text-align: left;
  opacity: 0.8;
}

.proxy-latency {
  font-weight: 600;
  font-size: 12px;
  white-space: nowrap;
  text-align: right;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 延迟状态颜色 */
.latency-fast { color: #67c23a; font-weight: 600; }
.latency-medium { color: #e6a23c; font-weight: 600; }
.latency-slow { color: #f56c6c; font-weight: 600; }
.latency-timeout { color: #909399; font-weight: normal; }
.latency-failed { color: #f44336; font-weight: 600; }

/* 空状态 */
.empty-state {
  padding: 30px 16px;
  text-align: center;
  color: var(--clash-text-secondary);
}

/* 加载状态 */
.loading-state {
  padding: 30px 16px;
  text-align: center;
  color: var(--clash-text-secondary);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

/* 顶部导航栏样式 */
:deep(.van-nav-bar) {
  background-color: var(--clash-primary);
}

:deep(.van-nav-bar__title),
:deep(.van-nav-bar__text),
:deep(.van-icon) {
  color: white !important;
}
</style> 