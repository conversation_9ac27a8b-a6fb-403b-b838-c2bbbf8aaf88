<template>
  <div v-if="modelValue" class="tip-dialog-overlay" @click="handleOverlayClick">
    <div class="tip-dialog" @click.stop>
      <div class="tip-dialog-header">
        <div class="tip-dialog-title">{{ title }}</div>
        <div class="tip-dialog-close" @click="close">
          <van-icon name="cross" />
        </div>
      </div>
      <div class="tip-dialog-content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TipDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    closeOnClickOverlay: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const close = () => {
      emit('update:modelValue', false);
    };

    const handleOverlayClick = () => {
      if (props.closeOnClickOverlay) {
        close();
      }
    };

    return {
      close,
      handleOverlayClick
    };
  }
};
</script>

<style scoped>
.tip-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.tip-dialog {
  background: var(--clash-background);
  border-radius: 8px;
  width: 90%;
  max-width: 420px;
  max-height: 90vh;
  overflow-y: auto;
  animation: dialog-fade-in 0.3s;
}

.tip-dialog-header {
  padding: 16px;
  border-bottom: 1px solid var(--clash-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tip-dialog-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--clash-text-primary);
}

.tip-dialog-close {
  cursor: pointer;
  padding: 4px;
}

.tip-dialog-close :deep(.van-icon) {
  font-size: 16px;
  color: var(--clash-text-secondary);
}

.tip-dialog-content {
  padding: 16px;
}

@keyframes dialog-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 