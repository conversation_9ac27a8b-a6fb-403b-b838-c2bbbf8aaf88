import { createRouter, createWebHistory } from 'vue-router';
import Provider from '../views/Provider.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: {
      title: 'Clash Mobile'
    }
  },
  {
    path: '/logs',
    name: 'Logs',
    component: () => import('../views/Logs.vue'),
    meta: {
      title: '日志'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/Settings.vue'),
    meta: {
      title: '设置'
    }
  },
  {
    path: '/provider',
    name: 'Provider',
    component: Provider,
    meta: {
      title: '提供者'
    }
  },
  {
    path: '/provider/proxies/:name',
    name: 'ProviderProxies',
    component: () => import('../views/ProviderProxies.vue'),
    meta: {
      title: '代理节点'
    }
  },
  {
    path: '/subscription-detail/:url',
    name: 'SubscriptionDetail',
    component: () => import('../views/SubscriptionDetail.vue'),
    meta: {
      title: '订阅详情'
    }
  },
  {
    path: '/dns-tool',
    name: 'DnsTool',
    component: () => import('../views/DnsTool.vue'),
    meta: {
      title: 'DNS查询工具'
    }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  next();
});

export default router; 