<template>
  <div class="settings clash-container">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="设置"
      :fixed="true"
      :placeholder="true"
    />

    <div class="settings-content">
      <div class="clash-card">
        <div class="section-title">API 设置</div>
        <van-cell-group inset>
          <van-cell title="API 地址">
            <template #right-icon>
              <van-field
                v-model="apiBaseUrl"
                placeholder="请输入 Clash API 地址"
                input-align="right"
                @blur="saveApiBaseUrl"
              />
            </template>
          </van-cell>
          <van-cell title="API 密钥">
            <template #right-icon>
              <van-field
                v-model="apiSecret"
                placeholder="请输入 API 密钥（可选）"
                input-align="right"
                @blur="saveApiSecret"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 状态信息卡片 -->
      <div class="clash-card">
        <div class="section-title">状态信息</div>
        <van-cell-group inset>
          <van-cell title="当前流量" :value="trafficInfo" />
          <van-cell title="内存使用" :value="memoryInfo" />
          <van-cell title="Clash 版本" :value="versionInfo" />
        </van-cell-group>
      </div>

      <!-- 缓存管理卡片 -->
      <div class="clash-card">
        <div class="section-title">缓存管理</div>
        <van-cell-group inset>
          <van-cell title="清除 FakeIP 缓存" is-link @click="flushFakeIPCache">
            <template #right-icon>
              <van-icon name="delete" />
            </template>
          </van-cell>
          <van-cell title="DNS查询工具" is-link @click="goToDnsTool">
            <template #right-icon>
              <van-icon name="search" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      

      <div class="clash-card">
        <div class="section-title">健康检测设置</div>
        <van-cell-group inset>
          <van-cell title="超时时间(ms)">
            <template #right-icon>
              <van-field
                v-model="timeout"
                type="number"
                placeholder="默认5000ms"
                input-align="right"
                @blur="saveTimeout"
              />
            </template>
          </van-cell>
          <van-cell title="测速链接">
            <template #right-icon>
              <van-field
                v-model="testUrl"
                placeholder="测速URL"
                input-align="right"
                @blur="saveTestUrl"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>


      <!-- 订阅设置卡片 -->
      <div class="clash-card">
        <div class="section-title">
          订阅设置
          <van-icon 
            name="question-o" 
            class="help-icon"
            @click="showSubscriptionTip = true"
          />
        </div>
        <tip-dialog
          v-model="showSubscriptionTip"
          title="订阅设置说明"
          :close-on-click-overlay="true"
        >
          <div class="subscription-tip">
            <p><strong>订阅链接：</strong></p>
            <p>标准 Clash YAML 格式，支持用 POST 进行更新（已携带 Clash 授权）</p>
            <p><strong>订阅更新 API：</strong></p>
            <p>接口定义同 clash-api /restart</p>
            <p>该接口至少需要在内部实现 clash restart api 的调用，可以增加额外 feature</p>
            <p><strong>最佳实践：</strong></p>
            <p>在调用api前，更新本地path对应的yaml文件，实现持久化更新订阅，避免手动重启后订阅还原</p>
            <p class="tip-note">（纯Web应用安全受限无法支持文件更新）</p>
          </div>
        </tip-dialog>
        <van-cell-group inset>
          <van-cell title="订阅链接">
            <template #right-icon>
              <van-field
                v-model="subscriptionUrl"
                placeholder="请输入订阅链接"
                input-align="right"
                @blur="saveSubscriptionUrl"
              />
            </template>
          </van-cell>
          <van-cell title="配置路径">
            <template #right-icon>
              <van-field
                v-model="subscriptionPath"
                placeholder="配置文件路径"
                input-align="right"
                @blur="saveSubscriptionPath"
              />
            </template>
          </van-cell>
          <van-cell title="订阅更新API">
            <template #right-icon>
              <van-field
                v-model="subscriptionUpdateApi"
                placeholder="可选，替代/restart接口"
                input-align="right"
                @blur="saveSubscriptionUpdateApi"
              />
            </template>
          </van-cell>
          <van-cell 
            title="查看订阅内容" 
            is-link 
            :clickable="!!subscriptionUrl"
            @click="viewSubscription"
          />
          <van-cell 
            title="刷新订阅" 
            is-link 
            :clickable="!!subscriptionUrl"
            @click="handleRefreshSubscription"
          >
            <template #right-icon>
              <van-icon name="replay" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>


      <!-- 基本配置卡片 -->
      <div class="clash-card">
        <div class="section-title">基本配置</div>
        <van-cell-group inset v-if="configInfo">
          <van-cell title="端口" :value="configInfo.port.toString()" />
          <van-cell title="Socks端口" :value="configInfo['socks-port'].toString()" />
          <van-cell title="混合端口" :value="configInfo['mixed-port'].toString()" />
          <van-cell title="允许局域网" :value="configInfo['allow-lan'] ? '是' : '否'" />
          <van-cell title="模式" :value="configInfo.mode" />
          <van-cell title="日志级别" :value="configInfo['log-level']" />
          
        </van-cell-group>
        <div v-else class="empty-state">
          <p>加载配置中...</p>
        </div>
      </div>

      <!-- 添加高级配置卡片 -->
      <div class="clash-card">
        <div class="section-title">高级配置</div>
        <van-cell-group inset v-if="configInfo">
          <van-cell 
            v-if="configInfo.tun" 
            title="TUN" 
            :value="`${configInfo.tun.enable ? '已开启' : '未开启'} / ${configInfo.tun.stack || '-'} / ${configInfo.tun.device || '-'}`" 
          />
          <van-cell title="统一延迟" :value="configInfo['unified-delay'] ? '是' : '否'" />
          <van-cell title="IPv6" :value="configInfo['ipv6'] ? '是' : '否'" />
          <van-cell title="接口名称" :value="configInfo['interface-name'] || '未设置'" />
          <van-cell title="TUIC服务器" :value="configInfo['tuic-server'] && configInfo['tuic-server'].enable ? '已开启' : '未开启'" />
          <van-cell title="TCP并发" :value="configInfo['tcp-concurrent'] ? '是' : '否'" />
          <van-cell title="入站TFO" :value="configInfo['inbound-tfo'] ? '是' : '否'" />
          <van-cell title="入站MPTCP" :value="configInfo['inbound-mptcp'] ? '是' : '否'" />
          <van-cell title="路由标记" :value="configInfo['routing-mark']?.toString() || '0'" />
          <van-cell title="GEO自动更新" :value="configInfo['geo-auto-update'] ? '是' : '否'" />
          <van-cell title="GEO更新间隔" :value="`${configInfo['geo-update-interval'] || 0}小时`" />
          <van-cell title="GEO数据模式" :value="configInfo['geodata-mode'] ? '是' : '否'" />
          <van-cell title="GEO数据加载器" :value="configInfo['geodata-loader'] || '未设置'" />
          <van-cell title="GEO站点匹配器" :value="configInfo['geosite-matcher'] || '未设置'" />
          <van-cell title="进程查找模式" :value="configInfo['find-process-mode'] || '未设置'" />
          <van-cell title="流量嗅探" :value="configInfo['sniffing'] ? '是' : '否'" />
          <van-cell title="全局指纹" :value="configInfo['global-client-fingerprint'] || '未设置'" />
          <van-cell title="全局UA" :value="configInfo['global-ua'] || '未设置'" />
          <van-cell title="ETag支持" :value="configInfo['etag-support'] ? '是' : '否'" />
          <van-cell title="保持空闲连接" :value="`${configInfo['keep-alive-idle'] && configInfo['keep-alive-idle'] > 0 ? configInfo['keep-alive-idle'] + '秒' : '无期限'}`" />
          <van-cell title="检测连接间隔" :value="`${configInfo['keep-alive-interval'] || 0}秒`" />
          <van-cell title="禁用保持连接" :value="configInfo['disable-keep-alive'] ? '是' : '否'" />
        </van-cell-group>
        <div v-else class="empty-state">
          <p>加载配置中...</p>
        </div>
      </div>

      <div class="clash-card">
        <div class="section-title">关于</div>
        <van-cell-group inset>
          <van-cell title="版本" value="1.0.0" />
          <van-cell title="开源协议" value="MIT" />
          <van-cell title="Github" is-link url="https://github.com/your-repo" />
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { showSuccessToast, showFailToast, showLoadingToast } from 'vant';
import axios from 'axios';
import TipDialog from '@/components/TipDialog.vue';

export default {
  name: 'Settings',
  components: {
    TipDialog
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    
    // 状态
    const showSubscriptionTip = ref(false);
    const apiBaseUrl = ref('');
    const apiSecret = ref('');
    const timeout = ref('5000');
    const testUrl = ref('http://www.gstatic.com/generate_204');
    const subscriptionUrl = ref('');
    const subscriptionPath = ref('/root/clash/config.yaml');
    const subscriptionUpdateApi = ref('');
    
    // 状态信息
    const trafficInfo = ref('加载中...');
    const memoryInfo = ref('加载中...');
    const versionInfo = ref('加载中...');
    const configInfo = ref(null);
    
    // WebSocket连接
    let trafficWs = null;
    let memoryWs = null;
    
    // 获取请求头
    const getHeaders = () => {
      const headers = {};
      if (store.state.apiSecret) {
        headers['Authorization'] = `Bearer ${store.state.apiSecret}`;
      }
      return headers;
    };
    
    // 获取WebSocket URL
    const getWebSocketUrl = (endpoint) => {
      const baseUrl = store.state.apiBaseUrl;
      // 将http(s)://替换为ws(s)://
      const wsBaseUrl = baseUrl.replace(/^http/, 'ws');
      // 构建完整URL
      let wsUrl = `${wsBaseUrl}/${endpoint}`;
      
      // 添加token参数(如果存在)
      if (store.state.apiSecret) {
        wsUrl += `?token=${store.state.apiSecret}`;
      }
      
      return wsUrl;
    };

    // 格式化流量数据
    const formatTraffic = (bytes) => {
      if (bytes < 1024) {
        return bytes.toFixed(2) + ' B';
      } else if (bytes < 1024 * 1024) {
        return (bytes / 1024).toFixed(2) + ' KB';
      } else if (bytes < 1024 * 1024 * 1024) {
        return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
      } else {
        return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
      }
    };

    // 初始化流量WebSocket连接
    const initTrafficWebSocket = () => {
      try {
        // 关闭之前的连接
        if (trafficWs && trafficWs.readyState !== WebSocket.CLOSED) {
          trafficWs.close();
        }
        
        const wsUrl = getWebSocketUrl('traffic');
        trafficWs = new WebSocket(wsUrl);
        
        trafficWs.onopen = () => {
          console.log('Traffic WebSocket连接已建立');
        };
        
        trafficWs.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            
            const up = formatTraffic(data.up);
            const down = formatTraffic(data.down);
            
            trafficInfo.value = `↑ ${up} / ↓ ${down}`;
          } catch (error) {
            console.error('处理流量数据失败:', error);
          }
        };
        
        trafficWs.onclose = () => {
          console.log('Traffic WebSocket连接已关闭');
          
          // 5秒后尝试重新连接
          setTimeout(() => {
            if (!trafficWs || trafficWs.readyState === WebSocket.CLOSED) {
              initTrafficWebSocket();
            }
          }, 5000);
        };
        
        trafficWs.onerror = (error) => {
          console.error('Traffic WebSocket连接错误:', error);
          trafficInfo.value = '未连接';
        };
      } catch (error) {
        console.error('初始化流量WebSocket失败:', error);
        trafficInfo.value = '未连接';
      }
    };

    // 初始化内存WebSocket连接
    const initMemoryWebSocket = () => {
      try {
        // 关闭之前的连接
        if (memoryWs && memoryWs.readyState !== WebSocket.CLOSED) {
          memoryWs.close();
        }
        
        const wsUrl = getWebSocketUrl('memory');
        memoryWs = new WebSocket(wsUrl);
        
        memoryWs.onopen = () => {
          console.log('Memory WebSocket连接已建立');
        };
        
        memoryWs.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            
            // 将内存转换为MB
            const inuseMemory = (data.inuse / (1024 * 1024)).toFixed(2);
            
            memoryInfo.value = `${inuseMemory} MB`
          } catch (error) {
            console.error('处理内存数据失败:', error);
          }
        };
        
        memoryWs.onclose = () => {
          console.log('Memory WebSocket连接已关闭');
          
          // 5秒后尝试重新连接
          setTimeout(() => {
            if (!memoryWs || memoryWs.readyState === WebSocket.CLOSED) {
              initMemoryWebSocket();
            }
          }, 5000);
        };
        
        memoryWs.onerror = (error) => {
          console.error('Memory WebSocket连接错误:', error);
          memoryInfo.value = '未连接';
        };
      } catch (error) {
        console.error('初始化内存WebSocket失败:', error);
        memoryInfo.value = '未连接';
      }
    };

    // 获取版本信息
    const fetchVersionInfo = async () => {
      try {
        const response = await axios.get(`${store.state.apiBaseUrl}/version`, { headers: getHeaders() });
        
        versionInfo.value = response.data.version;
      } catch (error) {
        console.error('Failed to fetch version info:', error);
        versionInfo.value = '未连接';
      }
    };

    // 获取基本配置
    const fetchConfigInfo = async () => {
      try {
        const response = await axios.get(`${store.state.apiBaseUrl}/configs`, { headers: getHeaders() });
        
        configInfo.value = response.data;
      } catch (error) {
        console.error('Failed to fetch config info:', error);
        showFailToast('获取配置信息失败');
      }
    };

    // 初始化静态信息
    const initStaticInfo = async () => {
      await Promise.all([
        fetchVersionInfo(),
        fetchConfigInfo()
      ]);
    };

    // 清除 FakeIP 缓存
    const flushFakeIPCache = async () => {
      try {
        await axios.post(`${store.state.apiBaseUrl}/cache/fakeip/flush`, {}, { headers: getHeaders() });
        showSuccessToast('FakeIP缓存已清除');
      } catch (error) {
        console.error('清除FakeIP缓存失败:', error);
        showFailToast('清除FakeIP缓存失败');
      }
    };
    
  
    const saveApiBaseUrl = () => {
      store.dispatch('setApiBaseUrl', apiBaseUrl.value);
      showSuccessToast('API地址已保存');
      
      // 重新初始化所有连接和数据
      initTrafficWebSocket();
      initMemoryWebSocket();
      initStaticInfo();
    };
    
    const saveApiSecret = () => {
      store.dispatch('setApiSecret', apiSecret.value);
      showSuccessToast('API密钥已保存');
      
      // 重新初始化所有连接和数据
      initTrafficWebSocket();
      initMemoryWebSocket();
      initStaticInfo();
    };
    
    const saveTimeout = () => {
      localStorage.setItem('timeout', timeout.value);
      showSuccessToast('测速超时时间已保存');
    };
    
    const saveTestUrl = () => {
      localStorage.setItem('testUrl', testUrl.value);
      showSuccessToast('测速链接已保存');
    };

    // 保存订阅链接
    const saveSubscriptionUrl = () => {
      store.dispatch('setSubscriptionUrl', subscriptionUrl.value);
      showSuccessToast('订阅链接已保存');
    };
    
    // 保存配置路径
    const saveSubscriptionPath = () => {
      store.dispatch('setSubscriptionPath', subscriptionPath.value);
      showSuccessToast('配置路径已保存');
    };
    
    // 保存订阅更新API
    const saveSubscriptionUpdateApi = () => {
      store.dispatch('setSubscriptionUpdateApi', subscriptionUpdateApi.value);
      showSuccessToast('订阅更新API已保存');
    };
    
    // 查看订阅内容
    const viewSubscription = () => {
      if (!subscriptionUrl.value) {
        showFailToast('请先配置订阅链接');
        return;
      }
      
      // 使用 encodeURIComponent 编码 URL
      const encodedUrl = encodeURIComponent(subscriptionUrl.value);
      router.push(`/subscription-detail/${encodedUrl}`);
    };
    
    // 刷新订阅
    const handleRefreshSubscription = async () => {
      if (!subscriptionUrl.value) {
        showFailToast('请先配置订阅链接');
        return;
      }
      
      const loadingToast = showLoadingToast({
        message: '正在刷新订阅...',
        forbidClick: true,
        duration: 0
      });
      
      try {
        const success = await store.dispatch('refreshSubscription');
        loadingToast.close();
        
        if (success) {
          showSuccessToast('订阅已刷新');
        } else {
          // 刷新失败，但不显示额外提示，因为store中已经显示了具体错误信息
        }
      } catch (error) {
        loadingToast.close();
        showFailToast('刷新订阅失败');
      }
    };
    
    // 跳转到DNS工具页面
    const goToDnsTool = () => {
      router.push('/dns-tool');
    };
    
    // 初始化
    onMounted(() => {
      console.log('Settings 组件已挂载');
      
      // 确保订阅配置已初始化
      store.dispatch('initSubscriptionConfig');
      
      // 从store加载API配置
      apiBaseUrl.value = store.state.apiBaseUrl;
      apiSecret.value = store.state.apiSecret;
      
      // 从localStorage加载测速配置
      timeout.value = localStorage.getItem('timeout') || '5000';
      testUrl.value = localStorage.getItem('testUrl') || 'http://www.gstatic.com/generate_204';
      
      // 从store加载订阅配置
      subscriptionUrl.value = store.state.subscriptionUrl;
      subscriptionPath.value = store.state.subscriptionPath;
      subscriptionUpdateApi.value = store.state.subscriptionUpdateApi;
      
      console.log('已加载订阅配置:', {
        url: subscriptionUrl.value,
        path: subscriptionPath.value,
        updateApi: subscriptionUpdateApi.value
      });
      
      // 初始化WebSocket连接
      initTrafficWebSocket();
      initMemoryWebSocket();
      
      // 加载静态信息
      initStaticInfo();
    });
    
    // 组件卸载前清理WebSocket连接
    onBeforeUnmount(() => {
      if (trafficWs) {
        trafficWs.close();
        trafficWs = null;
      }
      
      if (memoryWs) {
        memoryWs.close();
        memoryWs = null;
      }
    });
    
    return {
      apiBaseUrl,
      apiSecret,
      timeout,
      testUrl,
      subscriptionUrl,
      subscriptionPath,
      subscriptionUpdateApi,
      trafficInfo,
      memoryInfo,
      versionInfo,
      configInfo,
      showSubscriptionTip,
      saveApiBaseUrl,
      saveApiSecret,
      saveTimeout,
      saveTestUrl,
      saveSubscriptionUrl,
      saveSubscriptionPath,
      saveSubscriptionUpdateApi,
      viewSubscription,
      handleRefreshSubscription,
      flushFakeIPCache,
      goToDnsTool
    };
  }
};
</script>

<style scoped>
.settings {
  min-height: 100vh;
  padding-bottom: 80px;
  position: relative;
  overflow-y: auto;
}

.settings-content {
  padding: 12px;
  margin-bottom: 60px;
}

.section-title {
  padding: 10px 12px;
  font-size: 16px;
  font-weight: 500;
  color: var(--clash-text-primary);
  display: flex;
  align-items: center;
  gap: 4px;
  border-bottom: 1px solid var(--clash-border-color);
}

.help-icon {
  font-size: 16px;
  color: var(--clash-text-secondary);
  cursor: pointer;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: var(--clash-text-secondary);
}

/* 统一单元格样式 */
:deep(.van-cell) {
  padding: 10px 16px;
}

:deep(.van-cell__title) {
  flex: 1;
  min-width: 90px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.van-cell__value) {
  flex: 2;
  min-width: 120px;
  text-align: right;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 输入框样式 */
:deep(.van-field) {
  padding: 0;
  text-align: right;
  font-size: 14px;
}

:deep(.van-field__control) {
  text-align: right;
}

/* 卡片内边距调整 */
:deep(.van-cell-group--inset) {
  margin: 0 12px;
}

/* 图标样式 */
:deep(.van-icon) {
  font-size: 16px;
  color: var(--clash-text-secondary);
}

/* 清除缓存按钮样式 */
:deep(.van-cell--clickable) {
  padding-right: 12px;
}

.subscription-tip {
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
}

.subscription-tip p {
  margin: 8px 0;
}

.subscription-tip strong {
  font-weight: 500;
}

.tip-note {
  color: var(--clash-text-secondary);
  font-size: 12px;
  font-style: italic;
  margin-top: 12px;
}

:deep(.van-dialog) {
  max-width: 90%;
  border-radius: 8px;
}
</style> 