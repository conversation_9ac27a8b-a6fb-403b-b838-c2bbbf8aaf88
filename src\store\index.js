import { createStore } from 'vuex';
import axios from 'axios';
import { showFailToast } from 'vant';
import yaml from 'js-yaml';

// 设置默认API地址，实际项目中应该配置到环境变量中
const API_BASE_URL = 'http://clash-api.lan:1090';

export default createStore({
  state: {
    apiBaseUrl: API_BASE_URL,
    apiSecret: '', // API密钥
    proxyGroups: [], // 策略组
    proxies: {}, // 所有代理节点
    currentSelected: {}, // 当前已选节点 { 组名: 节点名 }
    loading: false,
    error: null,
    filterMode: 'none', // 'none' - 无过滤，'highAvailability' - 高可用
    groupUsageTime: {}, // 策略组使用时间记录 { 组名: 时间戳 }
    subscriptionUrl: '', // 订阅链接
    subscriptionPath: '/root/clash/config.yaml', // 配置文件路径
    subscriptionUpdateApi: '', // 订阅更新API地址
  },
  getters: {
    getProxyGroups: state => state.proxyGroups,
    getProxies: state => state.proxies,
    getCurrentSelected: state => state.currentSelected,
    isLoading: state => state.loading,
    getError: state => state.error,
    getFilterMode: state => state.filterMode,
    getGroupUsageTime: state => state.groupUsageTime,

    // 根据分组名获取分组中的代理
    getProxiesByGroup: (state) => (groupName) => {
      const group = state.proxyGroups.find(g => g.name === groupName);
      if (!group) return [];

      let proxies = group.all.map(proxyName => {
        const proxy = state.proxies[proxyName];

        let history = proxy.history;
        // 如果是URLTest类型且没有history数据但有now节点
        if ((!proxy.history || proxy.history.length === 0) && proxy.now && proxy.type === 'URLTest') {
          let currentProxy = proxy;
          while (currentProxy.now && (!currentProxy.history || currentProxy.history.length === 0)) {
            currentProxy = state.proxies[currentProxy.now];
          }

          history = currentProxy.history
        }

        let uncheck = true;
        let broken = false;
        let bad = false;
        let fine = false;

        //计算平均时延
        if(history && history.length > 0){

          let fineHistory = history.filter(record => record.delay > 0);
          let failHistory = history.filter(record => record.delay === 0);


          broken = failHistory.length > 0 && fineHistory.length > 0; // 是否断流
          bad = fineHistory.length === 0; // 是否超时
          fine = failHistory.length === 0; // 是否正常

          uncheck = false;

          proxy.averageDelay = Math.round(fineHistory.reduce((sum, record) => sum + record.delay, 0) / fineHistory.length);
          proxy.latestDelay = history[history.length - 1].delay;

          // 计算在线率
          proxy.onlineRate = Math.floor(((history.length - failHistory.length) / history.length) * 100);

        }

        return {
          ...proxy,
          history,
          uncheck,
          bad,
          broken,
          fine,
          selected: group.now === proxyName
        }
      });

      // 排序逻辑，根据代理组类型决定排序方式
      if (group.type === 'Selector' || group.type === 'URLTest' || group.type === 'Smart') {
        // Selector和URLTest类型按延迟排序
        proxies.sort((a, b) => {
          // fine节点优先,按平均延迟从低到高排序
          if (a.fine && b.fine) {
            return a.averageDelay - b.averageDelay;
          }
          if (a.fine) return -1;
          if (b.fine) return 1;

          // broken节点按在线率从高到低排序
          if (a.broken && b.broken) {
            if (a.onlineRate !== b.onlineRate) {
              return b.onlineRate - a.onlineRate;
            }
            return a.averageDelay - b.averageDelay;
          }
          if (a.broken) return -1;
          if (b.broken) return 1;

          // uncheck节点排在后面
          if (a.uncheck && b.uncheck) return 0;
          if (a.uncheck) return 1;
          if (b.uncheck) return -1;

          // bad节点排在最后
          if (a.bad) return 1;
          if (b.bad) return -1;

          return 0;
        });
      }
      // 对于Fallback类型，保持原始顺序，无需额外处理

      // 内置类型的代理节点（这些不受过滤影响）
      const builtInTypes = ['Selector', 'URLTest', 'Fallback','Smart', 'Direct', 'Reject', 'Pass'];

      // 高可用模式：只保留前10个fine的节点
      if (state.filterMode === 'highAvailability') {
        proxies = proxies.filter(proxy => {
          // 内置类型不受过滤影响
          if (builtInTypes.includes(proxy.type)) {
            return true;
          }

          if (!proxy.fine) {
            return false;
          }
          const fineNodes = proxies.filter(p => p.fine);
          const index = fineNodes.indexOf(proxy);
          return index < 20;
        });
      }

      return proxies;
    }
  },
  mutations: {
    SET_PROXY_GROUPS(state, groups) {
      state.proxyGroups = groups;
    },
    SET_PROXIES(state, proxies) {
      state.proxies = proxies;
    },
    SET_CURRENT_SELECTED(state, { groupName, proxyName }) {
      state.currentSelected = {
        ...state.currentSelected,
        [groupName]: proxyName
      };
    },
    SET_LOADING(state, status) {
      state.loading = status;
    },
    SET_ERROR(state, error) {
      state.error = error;
    },
    SET_FILTER_MODE(state, mode) {
      state.filterMode = mode;
    },
    SET_API_BASE_URL(state, url) {
      state.apiBaseUrl = url;
    },
    SET_API_SECRET(state, secret) {
      state.apiSecret = secret;
    },
    UPDATE_PROXY_LATENCY(state, { proxyName, delay }) {
      if (state.proxies[proxyName]) {
        if (!state.proxies[proxyName].history) {
          state.proxies[proxyName].history = [];
        }
        state.proxies[proxyName].history.push({
          time: new Date().toISOString(),
          delay
        });
      }
    },
    SET_GROUP_USAGE_TIME(state, { groupName, timestamp }) {
      state.groupUsageTime = {
        ...state.groupUsageTime,
        [groupName]: timestamp
      };
    },
    SET_SUBSCRIPTION_URL(state, url) {
      state.subscriptionUrl = url;
    },
    SET_SUBSCRIPTION_PATH(state, path) {
      state.subscriptionPath = path;
    },
    SET_SUBSCRIPTION_UPDATE_API(state, api) {
      state.subscriptionUpdateApi = api;
    }
  },
  actions: {
    // 设置API地址
    setApiBaseUrl({ commit }, url) {
      commit('SET_API_BASE_URL', url);
      localStorage.setItem('apiBaseUrl', url);
    },

    // 设置API密钥
    setApiSecret({ commit }, secret) {
      commit('SET_API_SECRET', secret);
      localStorage.setItem('apiSecret', secret);
    },

    // 初始化API配置
    initApiConfig({ commit, dispatch }) {
      const savedApiUrl = localStorage.getItem('apiBaseUrl');
      const savedApiSecret = localStorage.getItem('apiSecret');
      const savedGroupUsageTime = localStorage.getItem('groupUsageTime');

      if (savedApiUrl) {
        commit('SET_API_BASE_URL', savedApiUrl);
      }

      if (savedApiSecret) {
        commit('SET_API_SECRET', savedApiSecret);
      }

      if (savedGroupUsageTime) {
        try {
          const groupUsageTime = JSON.parse(savedGroupUsageTime);
          Object.entries(groupUsageTime).forEach(([groupName, timestamp]) => {
            commit('SET_GROUP_USAGE_TIME', { groupName, timestamp });
          });
        } catch (error) {
          console.error('Failed to parse saved group usage time:', error);
        }
      }
    },

    // 获取所有代理信息
    async fetchProxies({ commit, state }) {
      commit('SET_LOADING', true);
      commit('SET_ERROR', null);

      try {
        const headers = {};
        if (state.apiSecret) {
          headers['Authorization'] = `Bearer ${state.apiSecret}`;
        }

        const response = await axios.get(`${state.apiBaseUrl}/proxies`, { headers });
        const { proxies } = response.data;

        // 提取代理组
        let groups = Object.values(proxies)
          .filter(proxy => (proxy.type === 'Selector' && proxy.name !== 'GLOBAL') || proxy.type === 'Fallback' || proxy.type === 'URLTest' || proxy.type === 'Smart' || proxy.type === 'PASS')
          .map(group => ({
            name: group.name,
            type: group.type,
            now: group.now,
            all: group.all
          }));

        // 排序策略组
        groups.sort((a, b) => {
          // 首先根据使用时间排序
          const aTime = state.groupUsageTime[a.name] || 0;
          const bTime = state.groupUsageTime[b.name] || 0;

          // 如果两者都有使用时间记录，则最近使用的排在前面
          if (aTime && bTime) {
            return bTime - aTime; // 降序排列，最新的在前
          }

          // 如果只有一个有使用时间记录，则有记录的排在前面
          if (aTime && !bTime) return -1;
          if (!aTime && bTime) return 1;

          // 如果都没有使用时间记录，按照类型顺序排序：Selector > Fallback > URLTest
          if (a.type === b.type) return 0;
          if (a.type === 'Selector') return -1;
          if (b.type === 'Selector') return 1;
          if (a.type === 'Fallback') return -1;
          if (b.type === 'Fallback') return 1;
          return 0; // 如果都是URLTest或其他类型，保持原顺序
        });

        commit('SET_PROXY_GROUPS', groups);
        commit('SET_PROXIES', proxies);

        // 初始化当前选择
        const currentSelected = {};
        groups.forEach(group => {
          currentSelected[group.name] = group.now;
        });

        // 设置当前选择的代理
        groups.forEach(group => {
          commit('SET_CURRENT_SELECTED', {
            groupName: group.name,
            proxyName: group.now
          });
        });
      } catch (error) {
        console.error('Failed to fetch proxies:', error);
        const errorMsg = '无法获取代理信息，请检查API地址是否正确';
        commit('SET_ERROR', errorMsg);
        showFailToast(errorMsg);
      } finally {
        commit('SET_LOADING', false);
      }
    },

    // 根据代理名称获取并关闭相关连接
    async closeConnections({ state }, proxyName) {
      try {
        const headers = {};
        if (state.apiSecret) {
          headers['Authorization'] = `Bearer ${state.apiSecret}`;
        }

        // 获取所有活跃连接
        const response = await axios.get(`${state.apiBaseUrl}/connections`, { headers });
        const connections = response.data.connections || [];

        // 筛选出与指定代理相关的连接
        const relatedConnections = connections.filter(conn =>
          conn.chains && conn.chains.includes(proxyName)
        );

        // 并发关闭所有相关连接
        if (relatedConnections.length > 0) {
          await Promise.all(
            relatedConnections.map(conn =>
              axios.delete(`${state.apiBaseUrl}/connections/${conn.id}`, { headers })
            )
          );
        }

        return relatedConnections.length;
      } catch (error) {
        console.error('关闭代理相关连接失败:', error);
        return 0;
      }
    },

    // 切换代理节点
    async switchProxy({ commit, state }, { groupName, proxyName }) {
      commit('SET_LOADING', true);

      try {
        const headers = {};
        if (state.apiSecret) {
          headers['Authorization'] = `Bearer ${state.apiSecret}`;
        }

        await axios.put(`${state.apiBaseUrl}/proxies/${encodeURIComponent(groupName)}`, {
          name: proxyName
        }, { headers });

        commit('SET_CURRENT_SELECTED', { groupName, proxyName });

        // 更新本地代理组状态
        const updatedGroups = state.proxyGroups.map(group => {
          if (group.name === groupName) {
            return { ...group, now: proxyName };
          }
          return group;
        });

        commit('SET_PROXY_GROUPS', updatedGroups);

        // 更新使用时间记录
        const timestamp = Date.now();
        commit('SET_GROUP_USAGE_TIME', { groupName, timestamp });

        // 保存到本地存储
        localStorage.setItem('groupUsageTime', JSON.stringify({
          ...state.groupUsageTime,
          [groupName]: timestamp
        }));
      } catch (error) {
        console.error('Failed to switch proxy:', error);
        const errorMsg = '切换代理失败';
        commit('SET_ERROR', errorMsg);
        showFailToast(errorMsg);
      } finally {
        commit('SET_LOADING', false);
      }
    },

    // 测试策略组整体延迟
    async testProxyGroup({ commit, state }, groupName) {
      commit('SET_LOADING', true);

      try {
        const headers = {};
        if (state.apiSecret) {
          headers['Authorization'] = `Bearer ${state.apiSecret}`;
        }

        const timeout = localStorage.getItem('timeout') || '5000';
        const testUrl = localStorage.getItem('testUrl') || 'http://www.gstatic.com/generate_204';

        // 直接使用策略组的延迟测试接口
        await axios.get(`${state.apiBaseUrl}/group/${encodeURIComponent(groupName)}/delay`, {
          params: {
            timeout: parseInt(timeout),
            url: testUrl
          },
          headers
        });

        // 刷新代理信息以获取最新延迟数据
        await this.dispatch('fetchProxies');

      } catch (error) {
        console.error(`Failed to test proxy group ${groupName}:`, error);
        const errorMsg = '测试策略组延迟失败';
        commit('SET_ERROR', errorMsg);
        showFailToast(errorMsg);
      } finally {
        commit('SET_LOADING', false);
      }
    },

    // 设置过滤模式
    setFilterMode({ commit }, mode) {
      commit('SET_FILTER_MODE', mode);
    },

    // 获取所有代理数据
    async fetchData({ commit, dispatch }) {
      commit('SET_LOADING', true);
      commit('SET_ERROR', null);

      try {
        // 获取代理数据
        await dispatch('fetchProxies');
      } catch (error) {
        // 设置错误状态
        commit('SET_ERROR', '获取数据失败：' + error.message);
      } finally {
        // 设置加载状态为false
        commit('SET_LOADING', false);
      }
    },

    setSubscriptionUrl({ commit }, url) {
      localStorage.setItem('subscriptionUrl', url);
      commit('SET_SUBSCRIPTION_URL', url);
    },

    setSubscriptionPath({ commit }, path) {
      localStorage.setItem('subscriptionPath', path);
      commit('SET_SUBSCRIPTION_PATH', path);
    },

    setSubscriptionUpdateApi({ commit }, api) {
      localStorage.setItem('subscriptionUpdateApi', api);
      commit('SET_SUBSCRIPTION_UPDATE_API', api);
    },

    initSubscriptionConfig({ commit }) {
      // 从本地存储加载订阅配置
      const url = localStorage.getItem('subscriptionUrl') || '';
      const path = localStorage.getItem('subscriptionPath') || '/root/clash/config.yaml';
      const updateApi = localStorage.getItem('subscriptionUpdateApi') || '';

      console.log('初始化订阅配置:', { url, path, updateApi });

      commit('SET_SUBSCRIPTION_URL', url);
      commit('SET_SUBSCRIPTION_PATH', path);
      commit('SET_SUBSCRIPTION_UPDATE_API', updateApi);
    },

    async refreshSubscription({ state, commit }) {
      if (!state.subscriptionUrl) {
        showFailToast('请先配置订阅链接');
        return false;
      }

      try {
        // 获取请求头
        const getHeaders = (contentType = null) => {
          const headers = {};
          if (state.apiSecret) {
            headers['Authorization'] = `Bearer ${state.apiSecret}`;
          }
          if (contentType) {
            headers['Content-Type'] = contentType;
          }
          return headers;
        };

        // 获取订阅内容
        const response = await axios.get(state.subscriptionUrl, {
          headers: getHeaders()
        });
        const subscriptionContent = response.data;

        // 验证YAML格式
        try {
          yaml.load(subscriptionContent);
        } catch (yamlError) {
          console.error('订阅内容YAML格式无效:', yamlError);
          showFailToast(`订阅内容YAML格式无效: ${yamlError.message}`);
          return false;
        }

        // 保存到本地存储
        localStorage.setItem(`subscription_${state.subscriptionUrl}`, subscriptionContent);

        // 检查是否有自定义的更新API
        if (state.subscriptionUpdateApi) {
          // 使用自定义API更新订阅
          await axios.post(state.subscriptionUpdateApi, {
            path: state.subscriptionPath,
            payload: subscriptionContent
          }, {
            headers: getHeaders('application/json')
          });
        } else {
          // 使用Clash API的restart接口更新配置
          await axios.post(`${state.apiBaseUrl}/restart`, {
            path: state.subscriptionPath,
            payload: subscriptionContent
          }, {
            headers: getHeaders('application/json')
          });
        }

        return true;
      } catch (error) {
        console.error('刷新订阅失败:', error);
        showFailToast('刷新订阅失败');
        return false;
      }
    },

    async initData({ commit, dispatch }) {
      console.log('初始化应用数据...');
      commit('SET_LOADING', true);
      try {
        // 初始化 API 配置
        await dispatch('initApiConfig');
        console.log('API配置已初始化');

        // 初始化订阅配置
        dispatch('initSubscriptionConfig');
        console.log('订阅配置已初始化', localStorage.getItem('subscriptionUrl'), localStorage.getItem('subscriptionPath'), localStorage.getItem('subscriptionUpdateApi'));

        // 获取代理数据
        await dispatch('fetchProxies');
        console.log('代理数据已获取');
      } catch (error) {
        console.error('初始化数据失败:', error);
        commit('SET_ERROR', error.message);
      } finally {
        commit('SET_LOADING', false);
      }
    },
  }
});
