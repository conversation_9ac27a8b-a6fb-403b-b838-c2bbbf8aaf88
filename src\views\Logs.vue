<template>
  <div class="logs clash-container">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="日志"
      :fixed="true"
      :placeholder="true"
    >
    </van-nav-bar>

    <div class="logs-content">
      <!-- 连接模块 -->
      <div class="clash-card connections-card">
        <div class="section-title">
          <div class="title-left">
            <span>连接 ({{ connections.length }})</span>
            <div class="connection-status" :class="{'connected': isConnectionsConnected}">
              <span v-if="isConnectionsConnected">已连接</span>
              <span v-else>未连接</span>
            </div>
            <div class="traffic-info-container" v-if="downloadTotal > 0 || uploadTotal > 0">
              <div class="traffic-info-wrapper">
                <div class="traffic-info">
                  <span class="download">总下载 {{ formatTraffic(downloadTotal) }}</span>
                  <span class="upload">总上传 {{ formatTraffic(uploadTotal) }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="title-actions">
            <van-button size="small" type="danger" class="action-btn" @click="closeAllConnections" :disabled="connections.length === 0">关闭全部</van-button>
          </div>
        </div>

        <div v-if="connections.length === 0" class="empty-connections">
          <p>暂无活跃连接</p>
        </div>

        <div v-else class="connections-list">
          <div class="connections-container">
            <div
              v-for="conn in sortedConnections"
              :key="conn.id"
              class="connection-item"
              @click="showCloseConfirm(conn)">
              <div class="connection-title">
                <span class="title-text">{{ conn.metadata.host || conn.metadata.destinationIP }}</span>
                <div class="connection-info">
                  <span class="connection-traffic" v-if="conn.download > 0 || conn.upload > 0">
                    <span class="download-text">↓{{ formatTraffic(conn.download) }}</span>
                    <span class="upload-text">↑{{ formatTraffic(conn.upload) }}</span>
                  </span>
                  <span class="connection-start-time">{{ formatStartTime(conn.start) }}</span>
                </div>
              </div>
              <div class="connection-detail">
                <div class="connection-basic-info">{{ getBasicConnectionInfo(conn) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志模块 -->
      <div class="clash-card logs-card">
        <div class="section-title">
          <div class="title-left">
            <span>日志</span>
            <div class="connection-status" :class="{'connected': isConnected}">
              <span v-if="isConnected">已连接</span>
              <span v-else>未连接</span>
            </div>
          </div>
          <div class="title-actions">
            <div class="custom-dropdown" ref="dropdownRef">
              <div
                class="dropdown-trigger"
                @click="toggleDropdown"
                :class="{ 'active': showDropdown }">
                <span class="dropdown-title">{{ getLogLevelText() }}</span>
                <van-icon name="arrow-down" :class="{ 'rotated': showDropdown }" />
              </div>
              <div v-show="showDropdown" class="dropdown-menu">
                <div
                  v-for="option in logLevelOptions"
                  :key="option.value"
                  class="dropdown-item"
                  :class="{ 'active': selectedLogLevel === option.value }"
                  @click="selectLogLevel(option.value)">
                  {{ option.text.replace(/ \(.+\)$/, '') }}
                </div>
              </div>
            </div>
            <van-button size="small" type="danger" class="action-btn" @click="clearLogs" :disabled="filteredLogs.length === 0">
              <van-icon name="delete" size="14" class="btn-icon"/>
              <span>清空</span>
            </van-button>
          </div>
        </div>
        <div v-if="filteredLogs.length === 0" class="empty-logs">
          <p>暂无日志记录</p>
        </div>
        <div v-else class="logs-container">
          <div v-for="(log, index) in filteredLogs" :key="index" class="log-item">
            <div class="log-prefix">
              <span class="log-time">{{ formatTime(log.time) }} </span>
              <span class="log-level" :class="'log-level-' + log.type.toLowerCase()">{{ log.type }}</span>
            </div>

            <div class="log-payload">{{ log.payload }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { showFailToast, showSuccessToast, showConfirmDialog } from 'vant';
import axios from 'axios';

export default {
  name: 'Logs',
  setup() {
    const router = useRouter();
    const store = useStore();
    const logs = ref([]);
    const isConnected = ref(false);
    const ws = ref(null);
    const connectionsWs = ref(null);
    const isConnectionsConnected = ref(false);
    const connections = ref([]);
    const downloadTotal = ref(0);
    const uploadTotal = ref(0);

    // 添加日志级别筛选功能
    const selectedLogLevel = ref('info');
    const logLevelOptions = [
      { text: '调试 (Debug)', value: 'debug' },
      { text: '信息 (Info)', value: 'info' },
      { text: '警告 (Warning)', value: 'warning' },
      { text: '错误 (Error)', value: 'error' }
    ];

    // 下拉菜单状态
    const showDropdown = ref(false);
    const dropdownRef = ref(null);

    // 切换下拉菜单显示状态
    const toggleDropdown = () => {
      showDropdown.value = !showDropdown.value;
    };

    // 选择日志级别
    const selectLogLevel = (value) => {
      selectedLogLevel.value = value;
      showDropdown.value = false;
    };

    // 点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
        showDropdown.value = false;
      }
    };

    // 根据选择的日志级别过滤日志
    const filteredLogs = computed(() => {
      return logs.value.filter(log =>
        log.type.toLowerCase() === selectedLogLevel.value.toLowerCase()
      );
    });

    // 获取请求头
    const getHeaders = () => {
      const headers = {};
      if (store.state.apiSecret) {
        headers['Authorization'] = `Bearer ${store.state.apiSecret}`;
      }
      return headers;
    };

    // 获取连接WebSocket URL
    const getConnectionsWebSocketUrl = () => {
      const baseUrl = store.state.apiBaseUrl;
      // 将http(s)://替换为ws(s)://
      const wsBaseUrl = baseUrl.replace(/^http/, 'ws');
      // 添加路径
      let wsUrl = `${wsBaseUrl}/connections`;

      // 构建查询参数
      const params = new URLSearchParams();

      // 添加token参数（如果有）
      if (store.state.apiSecret) {
        params.append('token', store.state.apiSecret);
      }

      // 如果有参数，添加到URL
      const paramsString = params.toString();
      if (paramsString) {
        wsUrl += `?${paramsString}`;
      }

      return wsUrl;
    };

    // 初始化连接WebSocket
    const initConnectionsWebSocket = () => {
      try {
        const wsUrl = getConnectionsWebSocketUrl();

        // 关闭之前的连接
        if (connectionsWs.value && connectionsWs.value.readyState !== WebSocket.CLOSED) {
          connectionsWs.value.close();
        }

        // 创建新的WebSocket连接
        connectionsWs.value = new WebSocket(wsUrl);

        // 连接建立时的回调
        connectionsWs.value.onopen = () => {
          console.log('连接WebSocket连接已建立');
          isConnectionsConnected.value = true;
        };

        // 接收消息的回调
        connectionsWs.value.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            if (data.connections) {
              connections.value = Object.values(data.connections);
            }

            if (data.downloadTotal !== undefined) {
              downloadTotal.value = data.downloadTotal;
            }

            if (data.uploadTotal !== undefined) {
              uploadTotal.value = data.uploadTotal;
            }
          } catch (error) {
            console.error('处理连接消息失败:', error);
          }
        };

        // 连接关闭时的回调
        connectionsWs.value.onclose = () => {
          console.log('连接WebSocket连接已关闭');
          isConnectionsConnected.value = false;

          // 5秒后尝试重新连接
          setTimeout(() => {
            if (!connectionsWs.value || connectionsWs.value.readyState === WebSocket.CLOSED) {
              initConnectionsWebSocket();
            }
          }, 5000);
        };

        // 连接出错时的回调
        connectionsWs.value.onerror = (error) => {
          console.error('连接WebSocket连接错误:', error);
          isConnectionsConnected.value = false;
        };
      } catch (error) {
        console.error('初始化连接WebSocket失败:', error);
      }
    };

    // 格式化流量数据
    const formatTraffic = (bytes) => {
      if (bytes === 0) return '0 B';

      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));

      return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
    };

    // 关闭指定连接
    const closeConnection = async (id) => {
      try {
        await axios.delete(`${store.state.apiBaseUrl}/connections/${id}`, {
          headers: getHeaders()
        });
        showSuccessToast('连接已关闭');
      } catch (error) {
        console.error('关闭连接失败:', error);
        showFailToast('关闭连接失败');
      }
    };

    // 获取基础连接信息
    const getBasicConnectionInfo = (conn) => {
      let dnsMode = conn.metadata.dnsMode || '';
      dnsMode = dnsMode.replace('normal', '');
      const network = conn.metadata.network || '';
      const type = conn.rulePayload || '';
      const chains = conn.chains && conn.chains.length && conn.chains.join(' / ') || '';

      return ` ${network.toUpperCase()} ${dnsMode} ${type} (${chains})`;
    };

    // 获取WebSocket URL
    const getWebSocketUrl = () => {
      const baseUrl = store.state.apiBaseUrl;
      // 将http(s)://替换为ws(s)://
      const wsBaseUrl = baseUrl.replace(/^http/, 'ws');
      // 添加路径
      let wsUrl = `${wsBaseUrl}/logs`;

      // 构建查询参数
      const params = new URLSearchParams();

      // 添加token参数（如果有）
      if (store.state.apiSecret) {
        params.append('token', store.state.apiSecret);
      }

      // 添加日志级别参数
      params.append('level', selectedLogLevel.value);

      // 如果有参数，添加到URL
      const paramsString = params.toString();
      if (paramsString) {
        wsUrl += `?${paramsString}`;
      }

      return wsUrl;
    };

    // 初始化WebSocket连接
    const initWebSocket = () => {
      try {
        const wsUrl = getWebSocketUrl();

        // 关闭之前的连接
        if (ws.value && ws.value.readyState !== WebSocket.CLOSED) {
          ws.value.close();
        }

        // 创建新的WebSocket连接
        ws.value = new WebSocket(wsUrl);

        // 连接建立时的回调
        ws.value.onopen = () => {
          console.log('WebSocket连接已建立');
          isConnected.value = true;
        };

        // 接收消息的回调
        ws.value.onmessage = (event) => {
          try {
            const log = JSON.parse(event.data);
            // 确保时间戳格式一致
            if (!log.time) {
              log.time = new Date().toISOString();
            }
            // 将新日志添加到顶部
            logs.value.unshift(log);

            // 限制日志数量，避免内存占用过多
            if (logs.value.length > 50) {
              logs.value = logs.value.slice(0, 50);
            }
          } catch (error) {
            console.error('处理日志消息失败:', error);
          }
        };

        // 连接关闭时的回调
        ws.value.onclose = () => {
          console.log('WebSocket连接已关闭');
          isConnected.value = false;

          // 5秒后尝试重新连接
          setTimeout(() => {
            if (!ws.value || ws.value.readyState === WebSocket.CLOSED) {
              initWebSocket();
            }
          }, 5000);
        };

        // 连接出错时的回调
        ws.value.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          isConnected.value = false;
        };
      } catch (error) {
        console.error('初始化WebSocket失败:', error);
      }
    };

    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    };

    const clearLogs = () => {
      logs.value = [];
    };

    // 监听日志级别变化，重新连接WebSocket
    watch(selectedLogLevel, () => {
      initWebSocket();
    });

    // 监听API设置变化，重新连接WebSocket
    watch(() => [store.state.apiBaseUrl, store.state.apiSecret], () => {
      initWebSocket();
      initConnectionsWebSocket();
    });

    // 关闭所有连接
    const closeAllConnections = async () => {
      if (connections.value.length === 0) return;

      try {
        await axios.delete(`${store.state.apiBaseUrl}/connections`, {
          headers: getHeaders()
        });
        showSuccessToast('已关闭所有连接');
      } catch (error) {
        console.error('关闭所有连接失败:', error);
        showFailToast('关闭所有连接失败');
      }
    };

    // 获取当前选择的日志级别文本
    const getLogLevelText = () => {
      const option = logLevelOptions.find(opt => opt.value === selectedLogLevel.value);
      return option ? option.text.replace(/ \(.+\)$/, '') : '全部日志';
    };

    const formatStartTime = (timestamp) => {
      if (!timestamp) return '未知';
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date; // 毫秒差值

      // 转换为秒
      const seconds = Math.floor(diff / 1000);

      // 小于3秒显示"刚刚"
      if (seconds < 3) {
        return `刚刚`;
      }

      // 小于60秒显示"xx秒前"
      if (seconds < 60) {
        return `${seconds}秒前`;
      }

      // 小于60分钟显示"xx分钟前"
      const minutes = Math.floor(seconds / 60);
      if (minutes < 60) {
        return `${minutes}分钟前`;
      }

      // 小于24小时显示"xx小时前"
      const hours = Math.floor(minutes / 60);
      if (hours < 24) {
        return `${hours}小时前`;
      }

      // 大于等于24小时显示"xx天前"
      const days = Math.floor(hours / 24);
      return `${days}天前`;
    };

    // 按连接时间排序的连接列表
    const sortedConnections = computed(() => {
      return [...connections.value].sort((a, b) => {
        const timeA = new Date(a.start).getTime();
        const timeB = new Date(b.start).getTime();
        return timeB - timeA; // 降序排列，最新的在前
      });
    });

    // 显示关闭连接确认框
    const showCloseConfirm = async (conn) => {
      try {
        await showConfirmDialog({
          title: '关闭连接',
          message: `是否关闭连接 ${conn.metadata.host || conn.metadata.destinationIP}？`,
          confirmButtonText: '关闭',
          confirmButtonColor: '#ee0a24',
        });
        await closeConnection(conn.id);
      } catch (error) {
        // 用户取消或其他错误，不做处理
        console.log('用户取消或关闭连接失败:', error);
      }
    };

    onMounted(() => {
      initWebSocket();
      initConnectionsWebSocket();

      // 在组件卸载时清除资源
      onBeforeUnmount(() => {
        // 关闭WebSocket连接
        if (ws.value) {
          ws.value.close();
          ws.value = null;
        }

        // 关闭连接WebSocket
        if (connectionsWs.value) {
          connectionsWs.value.close();
          connectionsWs.value = null;
        }
      });

      // 添加点击外部关闭下拉菜单的事件监听
      document.addEventListener('click', handleClickOutside);
    });

    onBeforeUnmount(() => {
      // 移除事件监听
      document.removeEventListener('click', handleClickOutside);
    });

    return {
      logs,
      filteredLogs,
      isConnected,
      isConnectionsConnected,
      formatTime,
      clearLogs,
      selectedLogLevel,
      logLevelOptions,
      getLogLevelText,
      connections,
      sortedConnections,
      downloadTotal,
      uploadTotal,
      formatTraffic,
      closeConnection,
      closeAllConnections,
      getBasicConnectionInfo,
      showDropdown,
      dropdownRef,
      toggleDropdown,
      selectLogLevel,
      formatStartTime,
      showCloseConfirm
    };
  }
};
</script>

<style scoped>
.logs {
  min-height: 100vh;
  padding-bottom: 80px; /* 增加底部内边距，确保不被导航栏遮挡 */
  position: relative;
  overflow-y: auto;
}

.logs-content {
  padding: 12px;
  width: 100%;
  margin-bottom: 60px; /* 添加底部外边距 */
}

/* 连接模块样式 */
.connections-card {
  margin-bottom: 12px;
}

.section-title {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--clash-text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--clash-border-color);
}

.empty-connections {
  padding: 20px;
  text-align: center;
  color: var(--clash-text-secondary);
}

.connections-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 0 16px;
  margin-bottom: 12px;
}

.connections-container {
  border-radius: 8px;
  background-color: white;
  margin: 0;
  overflow: hidden;
}

.connection-item {
  padding: 10px 0;
  border-bottom: 1px solid var(--van-cell-border-color);
  cursor: pointer;
  transition: background-color 0.2s;
}

.connection-item:hover {
  background-color: var(--clash-hover-color, rgba(0, 0, 0, 0.02));
}

.connection-item:active {
  background-color: var(--clash-active-color, rgba(0, 0, 0, 0.05));
}

.connection-item:last-child {
  border-bottom: none;
}

.connection-title {
  font-weight: 500;
  font-size: 12px;
  color: var(--clash-text-primary);
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.connection-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-traffic {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
}

.download-text {
  color: var(--clash-primary);
}

.upload-text {
  color: var(--clash-warning);
}

.connection-start-time {
  font-size: 11px;
  color: var(--clash-text-secondary);
}

.connection-detail {
  font-size: 11px;
  color: var(--clash-text-secondary);
}

.title-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.title-actions .action-btn {
  height: 24px;
  padding: 0 4px;
  font-size: 12px;
  border-radius: 3px;
  font-weight: normal;
  display: flex;
  align-items: center;
  gap: 2px;
}

.btn-icon {
  margin-right: 2px;
}

/* 自定义下拉菜单样式 */
.custom-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  height: 24px;
  display: flex;
  align-items: center;
  background: var(--clash-primary-light);
  border-radius: 3px;
  padding: 0 8px;
  min-width: 48px;
  cursor: pointer;
  user-select: none;
  font-size: 12px;
  color: var(--clash-primary);
  gap: 4px;
}

.dropdown-trigger .van-icon {
  font-size: 12px;
  transition: transform 0.2s;
}

.dropdown-trigger .van-icon.rotated {
  transform: rotate(180deg);
}

.dropdown-title {
  font-size: 12px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  min-width: 60px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  margin-top: 4px;
  overflow: hidden;
}

.dropdown-item {
  padding: 8px 12px;
  font-size: 14px;
  white-space: nowrap;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item.active {
  color: var(--clash-primary);
  font-weight: 500;
}

/* 日志模块样式 */
.logs-card {
  position: relative;
  overflow: visible;
  padding-bottom: 12px;
}

.title-left {
  display: flex;
  align-items: center;
  gap: 5px;
}

.traffic-info-container {
  height: 18px;
  overflow: hidden;
  position: relative;
}

.traffic-info-wrapper {
  height: 100%;
  overflow: hidden;
}

.traffic-info {
  display: flex;
  flex-direction: column;
  animation: slideUpDown 6s infinite ease-in-out;
}

.traffic-info .download,
.traffic-info .upload {
  height: 18px;
  line-height: 18px;
  font-size: 12px;
}

.traffic-info .download {
  color: var(--clash-primary);
}

.traffic-info .upload {
  color: var(--clash-warning);
}

@keyframes slideUpDown {
  0%, 45% {
    transform: translateY(0);
  }
  50%, 95% {
    transform: translateY(-18px);
  }
  100% {
    transform: translateY(0);
  }
}

.connection-status {
  font-size: 12px;
  color: var(--clash-error);
  display: inline-flex;
  align-items: center;
  padding: 2px 4px;
  border-radius: 3px;
  background-color: rgba(244, 67, 54, 0.1);
}

.connection-status.connected {
  color: var(--clash-success);
  background-color: rgba(76, 175, 80, 0.1);
}

.empty-logs {
  padding: 20px;
  text-align: center;
  color: var(--clash-text-secondary);
}

.logs-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 0 16px 0 16px;
}

.log-item {
  padding: 8px 0;
  border-bottom: 1px solid var(--van-cell-border-color);
  font-size: 13px;
  line-height: 1.4;
}

.log-prefix {
  display: flex;
  align-items: center;
  gap: 4px;
}

.log-time {
  color: var(--clash-text-secondary);
  font-size: 12px;
}

.log-level {
  display: inline-block;
  padding: 0 4px;
  border-radius: 4px;
  font-size: 10px;
  color: #fff;
}

.log-level-debug {
  background-color: #5cBeA5;
}

.log-level-info {
  background-color: #2196f3;
}

.log-level-warning {
  background-color: #ff9800;
}

.log-level-error {
  background-color: #f44336;
}

.log-payload {
  word-break: break-word;
  margin-top: 6px;
  color: var(--clash-text-primary);
  font-size: 11px;
}

.connection-basic-info {
  margin-bottom: 2px;
}

</style>
