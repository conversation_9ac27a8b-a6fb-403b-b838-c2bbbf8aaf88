<template>
  <div class="app-container">
    <router-view v-slot="{ Component }">
      <keep-alive include="Home,Logs,Settings,Provider">
        <component :is="Component" />
      </keep-alive>
    </router-view>
    <BottomNav />
  </div>
</template>

<script>
import BottomNav from './components/BottomNav.vue';
import { useStore } from 'vuex';
import { onMounted } from 'vue';

export default {
  name: 'App',
  components: {
    BottomNav
  },
  setup() {
    const store = useStore();
    
    onMounted(() => {
      // 初始化所有配置已在main.js中完成，这里不需要重复调用
      // store.dispatch('initData');
    });
  }
};
</script>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #2c3e50;
  height: 100%;
  overflow-y: auto;
}


html, body {
  margin: 0;
  width: 100%;
  height: 100%; /* 回退 */
  height: -webkit-fill-available;
  height: 100dvh;
  max-height: 100%
}

/* 弹性布局防溢出 */
body {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

* {
  box-sizing: border-box;
}

.app-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: hidden; 
  overflow-x: hidden; /* 禁止水平滚动 */
  display: flex;
  flex-direction: column;
}
</style> 