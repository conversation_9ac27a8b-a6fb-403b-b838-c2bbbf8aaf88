<template>
  <div class="home">
    <!-- 顶部导航栏 -->
    <TheNavBar class="top-nav"
      :active-tab="activeTab"
      v-model:filter-mode="filterMode"
      @test-proxies="handleTestProxies"
      @refresh="manualRefresh"
    />

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 策略组选项卡 -->
      <van-tabs
        v-model:active="activeTab"
        swipeable
        lazy-render
        sticky
        title-active-color="var(--clash-primary)"
        color="var(--clash-primary)"
        class="tabs-container"
        @change="handleTabChange"
        :key="proxyGroupsKey">
        <van-tab
          v-for="group in proxyGroups"
          :key="group.name"
          :title="group.name">

          <!-- 代理节点容器 -->
          <div class="proxy-container">
            <!-- 下拉刷新组件 -->
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :head-height="50" :disabled="loading">
              <!-- 节点列表 -->
              <div class="proxy-grid">
                <div
                  v-for="(proxy, index) in getProxiesByGroup(group.name)"
                  :key="proxy.name + index"
                  class="proxy-item"
                  :class="{ 'selected': isSelected(group.name, proxy.name) }"
                  @click="selectProxy(group.name, proxy.name)">
                  <div class="proxy-top-row">
                    <span class="proxy-name">{{ proxy.name }}</span>
                    <span class="proxy-now" v-if="proxy.now">{{ proxy.now }}</span>
                  </div>
                  <div class="proxy-bottom-row">
                    <span class="proxy-type">{{ proxy.type }}</span>
                    <span
                      class="proxy-latency"
                      :class="latencyClass(proxy)">
                      {{ formatLatency(proxy) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 空状态显示 -->
              <div v-if="getProxiesByGroup(group.name).length === 0" class="empty-state">
                <p>此策略组中没有代理节点</p>
              </div>
              <div class="placeholder-space"></div>

            </van-pull-refresh>
          </div>
        </van-tab>
      </van-tabs>
    </div>

  </div>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useStore } from 'vuex';
import { showFailToast, showSuccessToast, showLoadingToast, closeToast } from 'vant';
import TheNavBar from '@/components/TheNavBar.vue';

export default {
  name: 'Home',
  components: {
    TheNavBar
  },
  setup() {
    const store = useStore();

    // 使用 ref 存储当前选中的标签页索引
    const activeTab = ref(0);

    // 策略组Key，用于强制重新渲染tabs
    const proxyGroupsKey = ref(0);

    // 过滤模式
    const filterMode = ref(localStorage.getItem('filterMode') || 'none');

    // 下拉刷新状态
    const refreshing = ref(false);

    // 计算属性
    const proxyGroups = computed(() => store.getters.getProxyGroups);
    const loading = computed(() => store.getters.isLoading);
    const error = computed(() => store.getters.getError);

    // 监听proxyGroups变化，当策略组顺序发生变化时，强制重新渲染tabs
    watch(proxyGroups, () => {
      proxyGroupsKey.value += 1;
    }, { immediate: false }); // 添加 immediate: false 避免初始化触发

    // 获取指定组的代理节点
    const getProxiesByGroup = (groupName) => {
      return store.getters.getProxiesByGroup(groupName);
    };

    // 判断节点是否被选中
    const isSelected = (groupName, proxyName) => {
      return store.getters.getCurrentSelected[groupName] === proxyName;
    };

    // 选择代理节点
    const selectProxy = (groupName, proxyName) => {
      let prevProxyName = store.getters.getCurrentSelected[groupName];
      store.dispatch('switchProxy', { groupName, proxyName });
      if(prevProxyName !== proxyName){
        store.dispatch('closeConnections', prevProxyName);
      }
    };

    // 下拉刷新处理
    const onRefresh = async () => {
      try {
        await store.dispatch('fetchProxies');
        showSuccessToast('刷新成功');
      } catch (error) {
        showFailToast(error.message || '刷新失败');
      } finally {
        refreshing.value = false;
      }
    };

    // 执行代理测试
    const handleTestProxies = async () => {
      try {
        // 获取当前活动的策略组
        const currentGroupName = proxyGroups.value[activeTab.value]?.name;

        if (currentGroupName) {
          // 显示加载提示
          const toast = showLoadingToast({
            message: '正在检测...',
            forbidClick: true,
            duration: 0,
          });

          // 执行测试
          await store.dispatch('testProxyGroup', currentGroupName);

          // 关闭加载提示
          closeToast();

          // 显示成功提示
          showSuccessToast('检测完成');
        }
      } catch (error) {
        closeToast();
        showFailToast(error.message || '检测失败');
      }
    };

    // 手动刷新数据
    const manualRefresh = async () => {
      try {
        await store.dispatch('fetchProxies');
        showSuccessToast('刷新成功');
      } catch (error) {
        // 错误由store处理，这里不需要额外提示
      }
    };

    // 计算占位空间高度
    const calculatePlaceholderHeight = (nodeCount) => {
      if (nodeCount === 0) return '0';

      const baseHeight = 40;
      const reduction = nodeCount * 8;
      const finalHeight = Math.max(0, baseHeight - reduction);

      return `${finalHeight}vh`;
    };

    // 格式化延迟信息
    const formatLatency = (proxy) => {
      if (proxy.uncheck) {
        return '待测试';
      }

      if (proxy.bad) {
        return proxy.type !== 'PASS' ? '离线' : '';
      } else if (proxy.broken) {
        return '断流 ' + proxy.onlineRate + '%';
      }

      return `${proxy.averageDelay} ms`;
    };

    // 获取延迟对应的样式类
    const latencyClass = (proxy) => {

      if (proxy.uncheck || proxy.bad) {
        return 'latency-timeout';
      } else if (proxy.averageDelay < 200) {
        return 'latency-fast';
      } else if (proxy.averageDelay < 500) {
        return 'latency-medium';
      } else if (proxy.averageDelay < 1000) {
        return 'latency-slow';
      } else {
        return 'latency-failed';
      }
    };

    // 处理选项卡切换
    const handleTabChange = (index) => {
      // 如果存在策略组，更新使用时间记录
      if (proxyGroups.value && proxyGroups.value[index]) {
        const groupName = proxyGroups.value[index].name;
        const timestamp = Date.now();

        // 直接保存到本地存储，下次启动时会读取
        let groupUsageTime = {};
        try {
          const savedData = localStorage.getItem('groupUsageTime');
          if (savedData) {
            groupUsageTime = JSON.parse(savedData);
          }
        } catch (error) {
          console.error('Failed to parse saved group usage time:', error);
        }

        groupUsageTime[groupName] = timestamp;
        localStorage.setItem('groupUsageTime', JSON.stringify(groupUsageTime));
      }
    };

    // 组件挂载时的处理
    onMounted(() => {
      // 设置过滤模式，使用 mutation 而不是 action
      store.commit('SET_FILTER_MODE', filterMode.value);

      // 监控过滤模式变化
      watch(filterMode, (newVal) => {
        localStorage.setItem('filterMode', newVal);
        // 避免不必要的 action 调用
        if (newVal !== store.state.filterMode) {
          store.dispatch('setFilterMode', newVal);
        }
      }, { immediate: false });

      // 初始化获取代理数据在App.vue和main.js中已经完成，这里不需要重复调用
      // store.dispatch('fetchData');
    });

    return {
      activeTab,
      proxyGroups,
      proxyGroupsKey,
      loading,
      error,
      filterMode,
      refreshing,
      getProxiesByGroup,
      isSelected,
      selectProxy,
      handleTestProxies,
      manualRefresh,
      formatLatency,
      latencyClass,
      calculatePlaceholderHeight,
      handleTabChange,
      onRefresh
    };
  }
};
</script>

<style scoped>
.home {
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: var(--clash-background);
  overflow: hidden;
  flex: 1;
}

.top-nav {
  flex-shrink: 0;
  z-index: 10;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* Tabs相关样式 */
:deep(.van-tabs) {
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.van-tabs__wrap) {
  flex-shrink: 0;
  z-index: 2;
}

:deep(.van-tabs__content) {
  flex: 1;
  overflow: hidden;
  position: relative;
}


:deep(.van-tab__panel) {
  height: 100% !important;
  overflow: hidden !important;
}

/* 代理节点容器 */
.proxy-container {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 8px 12px;
  box-sizing: border-box;
  overscroll-behavior: contain;
}

/* 下拉刷新组件样式 */
:deep(.van-pull-refresh) {
  overflow: visible;
  height: 100%;
}

:deep(.van-pull-refresh__track) {
  overflow: visible;
  display: flex;
  flex-direction: column;
}

:deep(.van-pull-refresh__head) {
  color: var(--clash-primary);
  z-index: 1;
}

/* 代理节点网格 */
.proxy-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 8px 0 50px;
}

.placeholder-space {
  flex: 1
}

/* 代理节点项 */
.proxy-item {
  background-color: var(--clash-item-bg);
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  transition: all 0.2s ease;
}

.proxy-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.proxy-item.selected {
  background-color: var(--clash-primary-light);
}

.proxy-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--clash-primary);
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

/* 代理节点顶部和底部行 */
.proxy-top-row {

  white-space: nowrap;
  overflow: hidden;

  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  text-overflow: ellipsis;
}

.proxy-name {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  color: var(--clash-text-primary);
}

.proxy-now {
  font-size: 10px;
  color: var(--clash-text-secondary);
}

.proxy-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  width: 100%;
  padding-top: 6px;
}

/* 代理类型和延迟 */
.proxy-type {
  color: var(--clash-text-secondary);
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  text-align: left;
  opacity: 0.8;
}

.proxy-latency {
  font-weight: 600;
  font-size: 12px;
  white-space: nowrap;
  text-align: right;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 延迟状态颜色 */
.latency-fast { color: #67c23a; font-weight: 600; }
.latency-medium { color: #e6a23c; font-weight: 600; }
.latency-slow { color: #f56c6c; font-weight: 600; }
.latency-timeout { color: #909399; font-weight: normal; }
.latency-failed { color: #f44336; font-weight: 600; }



/* 空状态 */
.empty-state {
  padding: 30px 16px;
  text-align: center;
  color: var(--clash-text-secondary);
}

/* 错误消息 */
.error-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 16px;
  border-radius: 4px;
  z-index: 1001;
}
</style>
