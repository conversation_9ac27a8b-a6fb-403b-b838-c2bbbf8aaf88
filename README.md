# Clash Mobile UI

基于Vue 3 + Vuex + Vue Router 4构建的移动端Clash控制面板，UI风格参考Clash for Android。

## 功能特点

- 基于Clash RESTful API进行接口交互
- 支持多种策略组展示和切换
- 节点健康检测
- 节点排序（默认、名称、延迟）
- 过滤不可用节点
- 完全响应式UI，适配各种移动设备
- 日志查看功能
- 可自定义设置

## 技术栈

- Vue 3
- Vuex 4
- Vue Router 4
- Vant UI
- Axios

## 开发设置

### 安装依赖
```
npm install
```

### 开发环境运行
```
npm run dev
```

### 构建生产版本
```
npm run build
```

## 使用说明

1. 确保已经启动Clash核心，并开启RESTful API
2. 在设置页面配置Clash API地址（默认为http://localhost:9090）
3. 在主页可以查看和切换各个策略组下的节点
4. 点击"健康检测"按钮测试所有节点延迟
5. 使用顶部的排序和过滤功能可以更好地管理节点列表

## API参考

本项目使用[Clash RESTful API](https://github.com/Dreamacro/clash/wiki/RESTful-API)进行通信。

## 许可证

[MIT](LICENSE) 