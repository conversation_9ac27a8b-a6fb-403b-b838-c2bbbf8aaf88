<template>
  <!-- 顶部导航栏 -->
  <van-nav-bar 
    :fixed="true" 
    :placeholder="true">
    <template #left>
      <div class="title-container">Clash Mobile</div>
    </template>
    <template #right>
      <div class="header-actions">
        
        <!-- 健康检测按钮 -->
        <van-icon 
          name="replay" 
          size="18" 
          color="#fff" 
          class="health-check-icon"
          :class="{'loading': loading}"
          @click="handleTestProxies" 
        />
        
        <!-- 过滤下拉菜单 -->
        <div class="filter-dropdown" ref="filterDropdownRef">
          <div 
            class="dropdown-trigger" 
            @click="toggleFilterDropdown">
            <van-icon name="filter-o" size="18" color="#fff" />
          </div>
          <div v-show="showFilterDropdown" class="custom-dropdown-menu">
            <div 
              v-for="option in filterOptions" 
              :key="option.value"
              class="dropdown-item"
              :class="{ 'active': localFilterMode === option.value }"
              @click="selectFilterOption(option.value)">
              {{ option.text }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </van-nav-bar>
</template>

<script>
import { computed, ref, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'TheNavBar',
  props: {
    activeTab: {
      type: Number,
      default: 0
    },
    filterMode: {
      type: String,
      default: 'none'
    }
  },
  emits: ['update:filterMode', 'test-proxies', 'refresh'],
  setup(props, { emit }) {
    const store = useStore();
    
    const localFilterMode = computed({
      get: () => props.filterMode,
      set: (val) => emit('update:filterMode', val)
    });
    
    // 过滤选项
    const filterOptions = [
      { text: '显示全部', value: 'none' },
      { text: '高可用节点', value: 'highAvailability' }
    ];
    
    // 计算属性
    const loading = computed(() => store.getters.isLoading);
    const proxyGroups = computed(() => store.getters.getProxyGroups);
    
    // 处理健康检测
    const handleTestProxies = async () => {
      emit('test-proxies');
    };
    
    // 处理刷新
    const handleRefresh = () => {
      emit('refresh');
    };
    
    // 过滤下拉菜单状态
    const showFilterDropdown = ref(false);
    const filterDropdownRef = ref(null);
    
    // 切换过滤下拉菜单
    const toggleFilterDropdown = () => {
      showFilterDropdown.value = !showFilterDropdown.value;
    };
    
    // 选择过滤选项
    const selectFilterOption = (value) => {
      localFilterMode.value = value;
      showFilterDropdown.value = false;
    };
    
    // 点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      if (filterDropdownRef.value && !filterDropdownRef.value.contains(event.target)) {
        showFilterDropdown.value = false;
      }
    };
    
    // 添加事件监听
    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
    });
    
    // 移除事件监听
    onBeforeUnmount(() => {
      document.removeEventListener('click', handleClickOutside);
    });
    
    return {
      loading,
      proxyGroups,
      filterOptions,
      handleTestProxies,
      handleRefresh,
      localFilterMode,
      showFilterDropdown,
      filterDropdownRef,
      toggleFilterDropdown,
      selectFilterOption
    };
  }
};
</script>

<style scoped>
/* 标题容器 */
.title-container {
  font-size: 18px;
  font-weight: 500;
  color: white;
  padding-left: 4px;
}

/* 健康检测图标 */
.health-check-icon {
  margin-right: 12px;
  transition: transform 0.3s;
}

.health-check-icon.loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 过滤下拉菜单 */
.filter-dropdown {
  position: relative;
}

.dropdown-trigger {
  cursor: pointer;
  padding: 4px;
}

/* 自定义下拉菜单 */
.custom-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 120px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  padding: 4px 0;
  margin-top: 6px;
  overflow: hidden;
}

.dropdown-item {
  color: #333;
  font-size: 14px;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: rgba(25, 137, 250, 0.05);
}

.dropdown-item.active {
  color: var(--clash-primary);
  font-weight: 500;
  background-color: rgba(25, 137, 250, 0.1);
}

/* 高可用节点选项特殊样式 */
.dropdown-item.active[class*="highAvailability"] {
  color: #10b981; /* 绿色 */
  background-color: rgba(16, 185, 129, 0.1);
}

.dropdown-item:hover[class*="highAvailability"] {
  background-color: rgba(16, 185, 129, 0.05);
}

/* 头部操作容器 */
.header-actions {
  display: flex;
  align-items: center;
}

/* 使用van-nav-bar的样式覆盖 */
:deep(.van-nav-bar) {
  background-color: var(--clash-primary);
  height: 50px;
  line-height: 50px;
}

:deep(.van-nav-bar__content) {
  height: 50px;
}

:deep(.van-nav-bar__left) {
  color: white;
}

:deep(.van-nav-bar__right) {
  color: white;
}
</style> 