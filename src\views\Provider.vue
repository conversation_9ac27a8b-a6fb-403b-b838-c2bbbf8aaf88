<template>
  <div class="provider">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="提供者"
      :fixed="true"
      :placeholder="true"
    />

    <div class="provider-container">
      <!-- 下拉刷新 -->
      <van-pull-refresh
        v-model="refreshing"
        @refresh="onRefresh"
        :head-height="50"
        success-text="刷新成功"
        pulling-text="下拉即可刷新"
        loosing-text="释放即可刷新"
      >
        <div class="provider-content">
          <!-- 代理集合面板 -->
          <div class="clash-card">
            <div class="section-title">
              <span>代理集合</span>
            </div>
            <van-cell-group>
              <template v-if="providers.length > 0">
                <van-cell v-for="provider in providers" :key="provider.name" @click="viewProxyProvider(provider.name)">
                  <template #title>
                    <div class="cell-content">
                      <div class="cell-title-container">
                        <span class="cell-title">{{ provider.name }}</span>
                        <span class="cell-time">{{ formatTime(provider.updatedAt) }}</span>
                      </div>
                      <div class="action-buttons">
                        <van-button size="small" type="primary" class="action-btn"
                          @click.stop="testProvider(provider.name)"
                          :loading="testingProviders[provider.name]"
                          loading-type="spinner"
                          loading-text="">测速</van-button>
                        <van-button size="small" type="primary" class="action-btn"
                          @click.stop="updateProvider(provider.name)"
                          :loading="updatingProviders[provider.name]"
                          loading-type="spinner"
                          loading-text="">更新</van-button>
                      </div>
                    </div>
                  </template>
                  <template #label>
                    <div class="provider-info">
                      <span>类型: {{ provider.type || '未知' }}</span>
                      <span>节点数: {{ provider.proxies?.length || 0 }}</span>
                      <span v-if="provider.uptime">存活: {{ provider.uptime }}%</span>
                      <span v-if="provider.stability">稳定: {{ provider.stability }}%</span>
                    </div>
                  </template>
                </van-cell>
              </template>
              <van-cell v-else>
                <template #title>
                  <div class="empty-tip">暂无代理集合</div>
                </template>
              </van-cell>
            </van-cell-group>
          </div>

          <!-- 规则集合面板 -->
          <div class="clash-card">
            <div class="section-title">
              <span>规则集合</span>
            </div>
            <van-cell-group inset>
              <template v-if="ruleProviders.length > 0">
                <van-cell v-for="provider in ruleProviders" :key="provider.name">
                  <template #title>
                    <div class="cell-content">
                      <div class="cell-title-container">
                        <span class="cell-title">{{ provider.name }}</span>
                        <span class="cell-time">{{ formatTime(provider.updatedAt) }}</span>
                      </div>
                      <div class="action-buttons">
                        <van-button size="small" type="primary" class="action-btn"
                          @click="updateRuleProvider(provider.name)"
                          :loading="updatingRuleProviders[provider.name]"
                          loading-type="spinner"
                          loading-text="">更新</van-button>
                      </div>
                    </div>
                  </template>
                  <template #label>
                    <div class="provider-info">
                      <span>类型: {{ provider.behavior }}</span>
                      <span>格式: {{ provider.format }}</span>
                      <span>规则数: {{ provider.ruleCount }}</span>
                    </div>
                  </template>
                </van-cell>
              </template>
              <van-cell v-else>
                <template #title>
                  <div class="empty-tip">暂无规则集合</div>
                </template>
              </van-cell>
            </van-cell-group>
          </div>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, reactive } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { showSuccessToast, showFailToast } from 'vant';
import axios from 'axios';

export default {
  name: 'Provider',
  setup() {
    const store = useStore();
    const router = useRouter();

    // 状态
    const providers = ref([]);
    const ruleProviders = ref([]);
    const refreshing = ref(false);

    // 按钮加载状态
    const testingProviders = reactive({});
    const updatingProviders = reactive({});
    const updatingRuleProviders = reactive({});

    // 获取请求头
    const getHeaders = () => {
      const headers = {};
      if (store.state.apiSecret) {
        headers['Authorization'] = `Bearer ${store.state.apiSecret}`;
      }
      return headers;
    };

    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return '未知';
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date; // 毫秒差值

      // 转换为秒
      const seconds = Math.floor(diff / 1000);

      // 小于3秒显示"刚刚"
      if (seconds < 3) {
        return `刚刚`;
      }

      // 小于60秒显示"xx秒前"
      if (seconds < 60) {
        return `${seconds}秒前`;
      }

      // 小于60分钟显示"xx分钟前"
      const minutes = Math.floor(seconds / 60);
      if (minutes < 60) {
        return `${minutes}分钟前`;
      }

      // 小于24小时显示"xx小时前"
      const hours = Math.floor(minutes / 60);
      if (hours < 24) {
        return `${hours}小时前`;
      }

      // 大于等于24小时显示"xx天前"
      const days = Math.floor(hours / 24);
      return `${days}天前`;
    };

    // 获取代理集合
    const fetchProviders = async () => {
      try {
        const response = await axios.get(`${store.state.apiBaseUrl}/providers/proxies`, { headers: getHeaders() });
        // 显示所有代理集合
        providers.value = Object.entries(response.data.providers)
          .map(([name, data]) => ({
            name,
            ...data
          }))
          .filter(provider => provider.vehicleType === 'HTTP');

        console.log('获取到代理集合:', providers.value);

        // 计算存活率和稳定率
        providers.value.forEach(provider => {
          if (!provider.proxies || !provider.proxies.length) {
            return;
          }

          // 统计有历史记录的节点数量
          let totalProxiesWithHistory = 0;
          // 统计存活的节点数量
          let aliveProxies = 0;
          // 统计稳定的节点数量
          let stableProxies = 0;

          provider.proxies.forEach(proxy => {
            if (!proxy.history || !proxy.history.length) return;

            totalProxiesWithHistory++;

            // 只要历史记录中有延迟不为0的记录,就算存活
            const isAlive = proxy.history.some(record => record.delay !== 0);
            if (isAlive) {
              aliveProxies++;
              // 对于存活的节点,检查是否稳定(所有历史记录延迟都不为0)
              const isStable = proxy.history.every(record => record.delay !== 0);
              if (isStable) {
                stableProxies++;
              }
            }
          });

          // 计算存活率和稳定率
          provider.uptime = totalProxiesWithHistory ? Math.round((aliveProxies / totalProxiesWithHistory) * 100) : 0;
          provider.stability = aliveProxies ? Math.round((stableProxies / aliveProxies) * 100) : 0;
        });

      } catch (error) {
        console.error('获取代理集合失败:', error);
        showFailToast('获取代理集合失败');
      }
    };

    // 获取规则集合
    const fetchRuleProviders = async () => {
      try {
        const response = await axios.get(`${store.state.apiBaseUrl}/providers/rules`, { headers: getHeaders() });
        // 只显示vehicleType为HTTP的集合
        ruleProviders.value = Object.entries(response.data.providers)
          .map(([name, data]) => ({
            name,
            ...data
          }))
          .filter(provider => provider.vehicleType === 'HTTP');
      } catch (error) {
        console.error('获取规则集合失败:', error);
        showFailToast('获取规则集合失败');
      }
    };

    // 更新代理集合
    const updateProvider = async (name) => {
      updatingProviders[name] = true;
      try {
        await axios.put(`${store.state.apiBaseUrl}/providers/proxies/${encodeURIComponent(name)}`, {}, { headers: getHeaders() });
        showSuccessToast('更新成功');
        await fetchProviders();
      } catch (error) {
        console.error('更新代理集合失败:', error);
        showFailToast(error?.response?.data?.message || "更新失败");
      } finally {
        updatingProviders[name] = false;
      }
    };

    // 更新规则集合
    const updateRuleProvider = async (name) => {
      updatingRuleProviders[name] = true;
      try {
        await axios.put(`${store.state.apiBaseUrl}/providers/rules/${encodeURIComponent(name)}`, {}, { headers: getHeaders() });
        showSuccessToast('更新成功');
        await fetchRuleProviders();
      } catch (error) {
        console.error('更新规则集合失败:', error);
        showFailToast(error?.response?.data?.message || "更新失败");
      } finally {
        updatingRuleProviders[name] = false;
      }
    };

    // 测试代理集合
    const testProvider = async (name) => {
      testingProviders[name] = true;
      try {
        const timeout = localStorage.getItem('timeout') || '5000';
        const testUrl = localStorage.getItem('testUrl') || 'http://www.gstatic.com/generate_204';

        await axios.get(`${store.state.apiBaseUrl}/providers/proxies/${encodeURIComponent(name)}/healthcheck`, {
          params: {
            timeout: parseInt(timeout),
            url: testUrl
          },
          headers: getHeaders()
        });

        showSuccessToast('测速完成');
        await fetchProviders();
      } catch (error) {
        console.error('测速失败:', error);
        showFailToast('测速失败');
      } finally {
        testingProviders[name] = false;
      }
    };

    // 查看代理集合详情
    const viewProxyProvider = (name) => {
      router.push(`/provider/proxies/${encodeURIComponent(name)}`);
    };

    // 下拉刷新
    const onRefresh = async () => {
      try {
        await Promise.all([fetchProviders(), fetchRuleProviders()]);
        showSuccessToast('刷新成功');
      } catch (error) {
        console.error('刷新失败:', error);
        showFailToast('刷新失败');
      } finally {
        refreshing.value = false;
      }
    };

    // 初始化
    onMounted(() => {
      fetchProviders();
      fetchRuleProviders();
    });

    return {
      providers,
      ruleProviders,
      refreshing,
      testingProviders,
      updatingProviders,
      updatingRuleProviders,
      updateProvider,
      updateRuleProvider,
      testProvider,
      viewProxyProvider,
      onRefresh,
      formatTime
    };
  }
};
</script>

<style scoped>
.provider {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: var(--clash-background);
  overflow: hidden;
}

.provider-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.provider-content {
  flex: 1;
  padding: 12px;
  box-sizing: border-box;
}
/* 下拉刷新组件样式 */
:deep(.van-pull-refresh) {
  overflow: visible;
  height: 100%;
}

:deep(.van-pull-refresh__track) {
  overflow: visible;
  display: flex;
  flex-direction: column;
}

:deep(.van-pull-refresh__head) {
  color: var(--clash-primary);
  z-index: 1;
}

/* 卡片样式 */
.clash-card {
  margin-bottom: 12px;
  background: var(--clash-foreground);
  border-radius: 8px;
  overflow: hidden;
}

/* 确保最后一个卡片也有底部间距 */
.clash-card:last-child {
  margin-bottom: 12px;
}

/* 空状态提示文本居中 */
.empty-tip {
  text-align: center;
  color: var(--clash-text-secondary);
  padding: 12px 0;
}

.section-title {
  padding: 10px 12px;
  font-size: 16px;
  font-weight: 500;
  color: var(--clash-text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--clash-border-color);
  position: relative;
  background-color: #fff;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 80px;
  height: 2px;
  background-color: var(--clash-primary);
}

.provider-info {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 12px;
  color: var(--clash-text-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

.cell-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.cell-title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.cell-title {
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.cell-time {
  font-size: 12px;
  color: var(--clash-text-secondary);
  margin-right: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  border-radius: 4px;
  font-weight: normal;
  min-width: 40px;
}

.empty-tip {
  text-align: center;
  color: var(--clash-text-secondary);
  padding: 10px 0;
  font-size: 14px;
}

:deep(.van-cell) {
  padding: 10px 12px;
}

:deep(.van-cell__title) {
  flex: 1;
  min-width: 90px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.van-cell__label) {
  margin-top: 4px;
}

:deep(.van-button) {
  height: 16px;
  padding: 0 6px;
  font-size: 11px;
  border-radius: 3px;
  font-weight: normal;
}

:deep(.van-button__loading) {
  font-size: 12px;
  width: 12px;
  height: 12px;
}

:deep(.van-loading__spinner) {
  width: 12px;
  height: 12px;
}

:deep(.van-cell-group--inset) {
  margin: 0;
  border-radius: 0;
}
</style>
