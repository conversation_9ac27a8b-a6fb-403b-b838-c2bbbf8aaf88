<template>
  <div class="bottom-nav">
    <van-tabbar route>
      <van-tabbar-item replace to="/" icon="home-o">主页</van-tabbar-item>
      <van-tabbar-item replace to="/provider" icon="cluster-o">提供者</van-tabbar-item>
      <van-tabbar-item replace to="/logs" icon="chart-trending-o">日志</van-tabbar-item>
      <van-tabbar-item replace to="/settings" icon="setting-o">设置</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
export default {
  name: 'BottomNav'
};
</script>

<style scoped>
.bottom-nav {
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: 50px;
  height: 50px;
}

:deep(.van-tabbar) {
  background-color: var(--clash-item-bg);
  border-top: 1px solid var(--clash-border);
}

:deep(.van-tabbar-item) {
  color: var(--clash-text-secondary);
}

:deep(.van-tabbar-item--active) {
  color: var(--clash-primary);
}
</style> 